---
trigger: manual
---

# Eweko API Codebase Documentation

## Project Overview
Eweko is a marketplace platform connecting farmers with buyers. The API is built with NestJS and MongoDB, following a modular architecture with clear separation of concerns.

## Tech Stack
- **Backend Framework**: NestJS (Node.js)
- **Database**: MongoDB with Mongoose ODM
- **API Documentation**: Swagger/OpenAPI
- **Validation**: class-validator and class-transformer
- **Authentication**: JWT (JSON Web Tokens)
- **Pagination**: Custom pagination service
- **Testing**: Jest (test directory present)

## Project Structure
```
src/
├── addresses/          # Address management
├── admin-tokens/       # Admin token management
├── auth/               # Authentication and authorization
├── cart/               # Shopping cart functionality
├── category/           # Product categories
├── crons/              # Scheduled tasks
├── notifications/      # Notification system
├── orders/             # Order management
├── otps/               # One-time password handling
├── payments/           # Payment processing
├── preferences/        # User preferences
├── produce/            # Core product management
├── shared/             # Shared utilities and modules
├── transactions/       # Transaction handling
├── users/              # User management
└── wallets/            # Digital wallet functionality
```

## Key Components

### 1. Produce Module
Handles product management with the following features:
- CRUD operations for products
- Category-based organization
- Farmer-product relationship
- Image handling
- Search and filtering

#### Schema (produce/schema.ts)
- `farmer`: Reference to User (Farmer)
- `category`: Reference to Category
- `name`: Product name
- `description`: Product description
- `price`: Current price
- `negotiablePrice`: Negotiable price
- `stock`: Available quantity
- `minOrderQty`: Minimum order quantity
- `harvestDate`: When the product was harvested
- `images`: Array of image URLs
- `slug`: URL-friendly identifier

### 2. Authentication
- JWT-based authentication
- Role-based access control
- OTP verification

### 3. Order Management
- Order creation and tracking
- Order status updates
- Order history

### 4. Payment System
- Payment processing
- Transaction history
- Wallet integration

## Code Style
- **Naming Conventions**:
  - Classes: PascalCase (e.g., `ProduceService`)
  - Variables and functions: camelCase
  - Constants: UPPER_SNAKE_CASE
  - Files: kebab-case (e.g., `create-produce.dto.ts`)

- **File Structure**:
  - Each module contains:
    - `module-name.module.ts`: Module definition
    - `module-name.controller.ts`: API endpoints
    - `module-name.service.ts`: Business logic
    - `dto/`: Data Transfer Objects
    - `schema.ts`: Database schema/model

- **Best Practices**:
  - Dependency Injection for testability
  - Input validation using class-validator
  - Comprehensive error handling
  - API documentation with Swagger decorators
  - Pagination for list endpoints

## Dependencies
- **Core**:
  - @nestjs/common
  - @nestjs/core
  - @nestjs/mongoose
  - mongoose
  - class-validator
  - class-transformer

- **Utilities**:
  - @nestjs/swagger
  - @nestjs/config
  - @nestjs/jwt
  - bcrypt
  - jsonwebtoken

## Development Setup
1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables in `.env` file

3. Run the application:
   ```bash
   npm run start:dev
   ```

4. Access Swagger documentation at `http://localhost:3000/api`

## Testing
Run tests using:
```bash
npm run test
```

## Deployment
- Docker configuration available
- Environment-based configuration
- Production-ready setup with error handling and logging

## Future Improvements
- Implement caching for better performance
- Add more comprehensive test coverage
- Enhance documentation with examples
- Implement rate limiting
- Add more robust error tracking and monitoring
