const { MongoClient } = require('mongodb');
const zlib = require('zlib');
const { promisify } = require('util');

const MONGODB_URI = 'mongodb+srv://eweko:<EMAIL>/?retryWrites=true&w=majority&appName=ewekodev';
const MONGODB_DATABASE = 'ewekodev';

const gunzip = promisify(zlib.gunzip);
const inflate = promisify(zlib.inflate);

async function decodeNestedData(data) {
  if (!data) return null;
  
  try {
    if (typeof data === 'string') {
      // Try base64 decode first
      try {
        const base64Decoded = Buffer.from(data, 'base64');
        
        // Try gzip decompression
        try {
          const gzipDecoded = await gunzip(base64Decoded);
          return JSON.parse(gzipDecoded.toString());
        } catch (gzipError) {
          // Try deflate decompression
          try {
            const deflateDecoded = await inflate(base64Decoded);
            return JSON.parse(deflateDecoded.toString());
          } catch (deflateError) {
            // Try direct JSON parse of base64 decoded
            try {
              return JSON.parse(base64Decoded.toString());
            } catch (jsonError) {
              return data;
            }
          }
        }
      } catch (base64Error) {
        // Try direct JSON parse
        try {
          return JSON.parse(data);
        } catch (jsonError) {
          return data;
        }
      }
    } else if (typeof data === 'object') {
      return data;
    }
    
    return data;
  } catch (error) {
    console.warn('Failed to decode nested data:', error.message);
    return data;
  }
}

async function inspectMongoData() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(MONGODB_DATABASE);
    
    // Inspect buyers collection
    console.log('\n=== BUYERS COLLECTION ===');
    const buyersCollection = db.collection('buyers');
    const buyers = await buyersCollection.find({}).limit(2).toArray();
    
    for (const buyer of buyers) {
      console.log(`\nBuyer ID: ${buyer._id}`);
      console.log('Raw buyer data:');
      console.log(JSON.stringify(buyer, null, 2));
      
      // Try to decode profile, contact, business if they exist
      if (buyer.profile) {
        console.log('\nDecoding profile...');
        const decodedProfile = await decodeNestedData(buyer.profile);
        console.log('Decoded profile:', JSON.stringify(decodedProfile, null, 2));
      }
      
      if (buyer.contact) {
        console.log('\nDecoding contact...');
        const decodedContact = await decodeNestedData(buyer.contact);
        console.log('Decoded contact:', JSON.stringify(decodedContact, null, 2));
      }
      
      if (buyer.business) {
        console.log('\nDecoding business...');
        const decodedBusiness = await decodeNestedData(buyer.business);
        console.log('Decoded business:', JSON.stringify(decodedBusiness, null, 2));
      }
    }
    
    // Inspect profiles collection
    console.log('\n\n=== PROFILES COLLECTION ===');
    const profilesCollection = db.collection('profiles');
    const profiles = await profilesCollection.find({}).limit(3).toArray();
    
    for (const profile of profiles) {
      console.log(`\nProfile ID: ${profile._id}`);
      console.log('Raw profile data:');
      console.log(JSON.stringify(profile, null, 2));
    }
    
    // Inspect contacts collection
    console.log('\n\n=== CONTACTS COLLECTION ===');
    const contactsCollection = db.collection('contacts');
    const contacts = await contactsCollection.find({}).limit(3).toArray();
    
    for (const contact of contacts) {
      console.log(`\nContact ID: ${contact._id}`);
      console.log('Raw contact data:');
      console.log(JSON.stringify(contact, null, 2));
    }
    
    // Inspect businesses collection
    console.log('\n\n=== BUSINESSES COLLECTION ===');
    const businessesCollection = db.collection('businesses');
    const businesses = await businessesCollection.find({}).limit(3).toArray();
    
    for (const business of businesses) {
      console.log(`\nBusiness ID: ${business._id}`);
      console.log('Raw business data:');
      console.log(JSON.stringify(business, null, 2));
    }
    
    // Inspect farmers collection
    console.log('\n\n=== FARMERS COLLECTION ===');
    const farmersCollection = db.collection('farmers');
    const farmers = await farmersCollection.find({}).limit(2).toArray();
    
    for (const farmer of farmers) {
      console.log(`\nFarmer ID: ${farmer._id}`);
      console.log('Raw farmer data:');
      console.log(JSON.stringify(farmer, null, 2));
      
      // Try to decode profile, contact, business if they exist
      if (farmer.profile) {
        console.log('\nDecoding farmer profile...');
        const decodedProfile = await decodeNestedData(farmer.profile);
        console.log('Decoded farmer profile:', JSON.stringify(decodedProfile, null, 2));
      }
      
      if (farmer.contact) {
        console.log('\nDecoding farmer contact...');
        const decodedContact = await decodeNestedData(farmer.contact);
        console.log('Decoded farmer contact:', JSON.stringify(decodedContact, null, 2));
      }
      
      if (farmer.business) {
        console.log('\nDecoding farmer business...');
        const decodedBusiness = await decodeNestedData(farmer.business);
        console.log('Decoded farmer business:', JSON.stringify(decodedBusiness, null, 2));
      }
    }
    
    // Check addresses collection
    console.log('\n\n=== ADDRESSES COLLECTION ===');
    const addressesCollection = db.collection('addresses');
    const addresses = await addressesCollection.find({}).limit(3).toArray();
    
    for (const address of addresses) {
      console.log(`\nAddress ID: ${address._id}`);
      console.log('Raw address data:');
      console.log(JSON.stringify(address, null, 2));
    }
    
  } catch (error) {
    console.error('Inspection failed:', error);
  } finally {
    await client.close();
  }
}

// Run the inspection
inspectMongoData().catch(console.error);
