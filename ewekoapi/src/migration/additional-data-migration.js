const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');

// MongoDB connection - Using the correct database with all data
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

// Helper function to get user ID by email from PostgreSQL
async function getUserIdByEmail(pgClient, email) {
  try {
    const result = await pgClient.query('SELECT id FROM users WHERE email = $1', [email]);
    return result.rows.length > 0 ? result.rows[0].id : null;
  } catch (error) {
    console.error(`Error finding user by email ${email}:`, error.message);
    return null;
  }
}

// Helper function to get user ID by MongoDB ObjectId reference
async function getUserIdByMongoRef(pgClient, mongoUserId, userEmailMap) {
  try {
    // First try to find by email if we have the mapping
    const email = userEmailMap.get(mongoUserId.toString());
    if (email) {
      return await getUserIdByEmail(pgClient, email);
    }
    
    // If no email mapping, try to find by username (fallback)
    const result = await pgClient.query('SELECT id FROM users WHERE username LIKE $1', [`%${mongoUserId}%`]);
    return result.rows.length > 0 ? result.rows[0].id : null;
  } catch (error) {
    console.error(`Error finding user by mongo ref ${mongoUserId}:`, error.message);
    return null;
  }
}

async function migratePreferences() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('\n=== MIGRATING PREFERENCES ===');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    const preferencesCollection = db.collection('preferences');
    const preferences = await preferencesCollection.find({}).toArray();
    
    console.log(`Found ${preferences.length} preferences to migrate`);
    
    // Create user email mapping for reference lookup
    const usersResult = await pgClient.query('SELECT id, email, username FROM users');
    const userEmailMap = new Map();
    usersResult.rows.forEach(user => {
      userEmailMap.set(user.email, user.id);
      if (user.username) {
        userEmailMap.set(user.username, user.id);
      }
    });
    
    let migratedCount = 0;
    
    for (const preference of preferences) {
      try {
        // Find the user ID
        let userId = null;
        
        if (preference.user) {
          userId = await getUserIdByMongoRef(pgClient, preference.user, userEmailMap);
        } else if (preference.userId) {
          userId = await getUserIdByMongoRef(pgClient, preference.userId, userEmailMap);
        }
        
        if (!userId) {
          console.log(`Skipping preference ${preference._id} - user not found`);
          continue;
        }
        
        const preferenceData = {
          id: crypto.randomUUID(),
          user_id: userId,
          notification_settings: JSON.stringify(preference.notificationSettings || preference.notification_settings || {}),
          privacy_settings: JSON.stringify(preference.privacySettings || preference.privacy_settings || {}),
          language: preference.language || 'en',
          theme: preference.theme || 'light',
          currency: preference.currency || 'NGN',
          timezone: preference.timezone || 'Africa/Lagos',
          created_at: preference.createdAt || new Date(),
          updated_at: preference.updatedAt || new Date()
        };
        
        const insertPreferenceQuery = `
          INSERT INTO preferences (
            id, user_id, notification_settings, privacy_settings, language, theme, currency, timezone, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          ON CONFLICT (user_id) DO UPDATE SET
            notification_settings = EXCLUDED.notification_settings,
            privacy_settings = EXCLUDED.privacy_settings,
            language = EXCLUDED.language,
            theme = EXCLUDED.theme,
            currency = EXCLUDED.currency,
            timezone = EXCLUDED.timezone,
            updated_at = EXCLUDED.updated_at
        `;
        
        await pgClient.query(insertPreferenceQuery, [
          preferenceData.id, preferenceData.user_id, preferenceData.notification_settings,
          preferenceData.privacy_settings, preferenceData.language, preferenceData.theme,
          preferenceData.currency, preferenceData.timezone, preferenceData.created_at, preferenceData.updated_at
        ]);
        
        migratedCount++;
        console.log(`Migrated preference for user: ${userId}`);
        
      } catch (error) {
        console.error(`Error migrating preference ${preference._id}:`, error.message);
      }
    }
    
    console.log(`Successfully migrated ${migratedCount} preferences`);
    
  } catch (error) {
    console.error('Preferences migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateNotifications() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('\n=== MIGRATING NOTIFICATIONS ===');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    const notificationsCollection = db.collection('notifications');
    const notifications = await notificationsCollection.find({}).toArray();
    
    console.log(`Found ${notifications.length} notifications to migrate`);
    
    // Create user email mapping for reference lookup
    const usersResult = await pgClient.query('SELECT id, email, username FROM users');
    const userEmailMap = new Map();
    usersResult.rows.forEach(user => {
      userEmailMap.set(user.email, user.id);
      if (user.username) {
        userEmailMap.set(user.username, user.id);
      }
    });
    
    let migratedCount = 0;
    
    for (const notification of notifications) {
      try {
        // Find the user ID
        let userId = null;
        
        if (notification.user) {
          userId = await getUserIdByMongoRef(pgClient, notification.user, userEmailMap);
        } else if (notification.userId) {
          userId = await getUserIdByMongoRef(pgClient, notification.userId, userEmailMap);
        }
        
        if (!userId) {
          console.log(`Skipping notification ${notification._id} - user not found`);
          continue;
        }
        
        const notificationData = {
          id: crypto.randomUUID(),
          user_id: userId,
          title: notification.title || 'Notification',
          message: notification.message || notification.body || '',
          type: notification.type || 'info',
          is_read: notification.isRead || notification.is_read || false,
          data: JSON.stringify(notification.data || {}),
          created_at: notification.createdAt || new Date(),
          updated_at: notification.updatedAt || new Date()
        };
        
        const insertNotificationQuery = `
          INSERT INTO notifications (
            id, user_id, title, message, type, is_read, data, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `;
        
        await pgClient.query(insertNotificationQuery, [
          notificationData.id, notificationData.user_id, notificationData.title,
          notificationData.message, notificationData.type, notificationData.is_read,
          notificationData.data, notificationData.created_at, notificationData.updated_at
        ]);
        
        migratedCount++;
        console.log(`Migrated notification: ${notificationData.title}`);
        
      } catch (error) {
        console.error(`Error migrating notification ${notification._id}:`, error.message);
      }
    }
    
    console.log(`Successfully migrated ${migratedCount} notifications`);
    
  } catch (error) {
    console.error('Notifications migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateOrders() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('\n=== MIGRATING ORDERS ===');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    const ordersCollection = db.collection('orders');
    const orders = await ordersCollection.find({}).toArray();
    
    console.log(`Found ${orders.length} orders to migrate`);
    
    // Get user and produce mappings
    const usersResult = await pgClient.query('SELECT id, email, username FROM users');
    const userEmailMap = new Map();
    usersResult.rows.forEach(user => {
      userEmailMap.set(user.email, user.id);
      if (user.username) {
        userEmailMap.set(user.username, user.id);
      }
    });
    
    const producesResult = await pgClient.query('SELECT id, name, slug FROM produces');
    const produceMap = new Map();
    producesResult.rows.forEach(produce => {
      produceMap.set(produce.name.toLowerCase(), produce.id);
      produceMap.set(produce.slug, produce.id);
    });
    
    let migratedCount = 0;
    
    for (const order of orders) {
      try {
        // Find buyer ID
        let buyerId = null;
        if (order.buyer) {
          buyerId = await getUserIdByMongoRef(pgClient, order.buyer, userEmailMap);
        }
        
        if (!buyerId) {
          console.log(`Skipping order ${order._id} - buyer not found`);
          continue;
        }
        
        const orderData = {
          id: crypto.randomUUID(),
          buyer_id: buyerId,
          order_number: order.orderNumber || order.order_number || `ORD-${Date.now()}`,
          status: order.status || 'pending',
          total_amount: order.totalAmount || order.total_amount || 0,
          delivery_fee: order.deliveryFee || order.delivery_fee || 0,
          payment_method: order.paymentMethod || order.payment_method || 'cash',
          payment_status: order.paymentStatus || order.payment_status || 'pending',
          delivery_address: JSON.stringify(order.deliveryAddress || order.delivery_address || {}),
          notes: order.notes || null,
          created_at: order.createdAt || new Date(),
          updated_at: order.updatedAt || new Date()
        };
        
        const insertOrderQuery = `
          INSERT INTO orders (
            id, buyer_id, order_number, status, total_amount, delivery_fee, payment_method, 
            payment_status, delivery_address, notes, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          ON CONFLICT (order_number) DO NOTHING
          RETURNING id
        `;
        
        const orderResult = await pgClient.query(insertOrderQuery, [
          orderData.id, orderData.buyer_id, orderData.order_number, orderData.status,
          orderData.total_amount, orderData.delivery_fee, orderData.payment_method,
          orderData.payment_status, orderData.delivery_address, orderData.notes,
          orderData.created_at, orderData.updated_at
        ]);
        
        if (orderResult.rows.length === 0) {
          console.log(`Order ${orderData.order_number} already exists, skipping...`);
          continue;
        }
        
        const insertedOrderId = orderResult.rows[0].id;
        
        // Migrate order items if they exist
        if (order.items && Array.isArray(order.items)) {
          for (const item of order.items) {
            try {
              // Find produce ID (this might need adjustment based on how produce references work)
              let produceId = null;
              if (item.produce) {
                produceId = produceMap.get(item.produce.toLowerCase());
              } else if (item.produceName) {
                produceId = produceMap.get(item.produceName.toLowerCase());
              }
              
              if (!produceId) {
                console.log(`Skipping order item - produce not found: ${item.produce || item.produceName}`);
                continue;
              }
              
              const orderItemData = {
                id: crypto.randomUUID(),
                order_id: insertedOrderId,
                produce_id: produceId,
                quantity: item.quantity || 1,
                unit_price: item.unitPrice || item.unit_price || 0,
                total_price: item.totalPrice || item.total_price || (item.quantity * item.unitPrice) || 0,
                created_at: order.createdAt || new Date(),
                updated_at: order.updatedAt || new Date()
              };
              
              const insertOrderItemQuery = `
                INSERT INTO order_items (
                  id, order_id, produce_id, quantity, unit_price, total_price, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
              `;
              
              await pgClient.query(insertOrderItemQuery, [
                orderItemData.id, orderItemData.order_id, orderItemData.produce_id,
                orderItemData.quantity, orderItemData.unit_price, orderItemData.total_price,
                orderItemData.created_at, orderItemData.updated_at
              ]);
              
            } catch (itemError) {
              console.error(`Error migrating order item:`, itemError.message);
            }
          }
        }
        
        migratedCount++;
        console.log(`Migrated order: ${orderData.order_number}`);
        
      } catch (error) {
        console.error(`Error migrating order ${order._id}:`, error.message);
      }
    }
    
    console.log(`Successfully migrated ${migratedCount} orders`);
    
  } catch (error) {
    console.error('Orders migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateTransactions() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('\n=== MIGRATING TRANSACTIONS ===');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    const transactionsCollection = db.collection('transactions');
    const transactions = await transactionsCollection.find({}).toArray();
    
    console.log(`Found ${transactions.length} transactions to migrate`);
    
    // Get user mappings
    const usersResult = await pgClient.query('SELECT id, email, username FROM users');
    const userEmailMap = new Map();
    usersResult.rows.forEach(user => {
      userEmailMap.set(user.email, user.id);
      if (user.username) {
        userEmailMap.set(user.username, user.id);
      }
    });
    
    let migratedCount = 0;
    
    for (const transaction of transactions) {
      try {
        // Find user ID
        let userId = null;
        if (transaction.user) {
          userId = await getUserIdByMongoRef(pgClient, transaction.user, userEmailMap);
        } else if (transaction.userId) {
          userId = await getUserIdByMongoRef(pgClient, transaction.userId, userEmailMap);
        }
        
        if (!userId) {
          console.log(`Skipping transaction ${transaction._id} - user not found`);
          continue;
        }
        
        const transactionData = {
          id: crypto.randomUUID(),
          user_id: userId,
          reference: transaction.reference || `TXN-${Date.now()}`,
          amount: transaction.amount || 0,
          type: transaction.type || 'payment',
          status: transaction.status || 'pending',
          description: transaction.description || null,
          payment_method: transaction.paymentMethod || transaction.payment_method || null,
          external_reference: transaction.externalReference || transaction.external_reference || null,
          metadata: JSON.stringify(transaction.metadata || {}),
          created_at: transaction.createdAt || new Date(),
          updated_at: transaction.updatedAt || new Date()
        };
        
        const insertTransactionQuery = `
          INSERT INTO transactions (
            id, user_id, reference, amount, type, status, description, payment_method, 
            external_reference, metadata, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          ON CONFLICT (reference) DO NOTHING
        `;
        
        await pgClient.query(insertTransactionQuery, [
          transactionData.id, transactionData.user_id, transactionData.reference,
          transactionData.amount, transactionData.type, transactionData.status,
          transactionData.description, transactionData.payment_method,
          transactionData.external_reference, transactionData.metadata,
          transactionData.created_at, transactionData.updated_at
        ]);
        
        migratedCount++;
        console.log(`Migrated transaction: ${transactionData.reference}`);
        
      } catch (error) {
        console.error(`Error migrating transaction ${transaction._id}:`, error.message);
      }
    }
    
    console.log(`Successfully migrated ${migratedCount} transactions`);
    
  } catch (error) {
    console.error('Transactions migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function runAdditionalDataMigration() {
  console.log('Starting additional data migration from MongoDB to PostgreSQL...\n');
  console.log('This script migrates preferences, notifications, orders, and transactions.\n');
  
  try {
    // Step 1: Migrate preferences
    console.log('=== STEP 1: Migrating Preferences ===');
    await migratePreferences();
    
    // Step 2: Migrate notifications
    console.log('\n=== STEP 2: Migrating Notifications ===');
    await migrateNotifications();
    
    // Step 3: Migrate orders
    console.log('\n=== STEP 3: Migrating Orders ===');
    await migrateOrders();
    
    // Step 4: Migrate transactions
    console.log('\n=== STEP 4: Migrating Transactions ===');
    await migrateTransactions();
    
    console.log('\n=== ADDITIONAL DATA MIGRATION COMPLETED ===');
    console.log('All additional data has been successfully migrated from MongoDB to PostgreSQL');
    
  } catch (error) {
    console.error('Additional data migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runAdditionalDataMigration().catch(console.error);
}

module.exports = { 
  migratePreferences, 
  migrateNotifications, 
  migrateOrders, 
  migrateTransactions, 
  runAdditionalDataMigration 
};
