const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://eweko:<EMAIL>/?retryWrites=true&w=majority&appName=ewekodev';
const MONGODB_DATABASE = 'ewekodev';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

// Utility functions for decoding nested data
const gunzip = promisify(zlib.gunzip);
const inflate = promisify(zlib.inflate);

async function decodeNestedData(data) {
  if (!data) return null;
  
  try {
    // Try different decoding methods
    if (typeof data === 'string') {
      // Try base64 decode first
      try {
        const base64Decoded = Buffer.from(data, 'base64');
        
        // Try gzip decompression
        try {
          const gzipDecoded = await gunzip(base64Decoded);
          return JSON.parse(gzipDecoded.toString());
        } catch (gzipError) {
          // Try deflate decompression
          try {
            const deflateDecoded = await inflate(base64Decoded);
            return JSON.parse(deflateDecoded.toString());
          } catch (deflateError) {
            // Try direct JSON parse of base64 decoded
            try {
              return JSON.parse(base64Decoded.toString());
            } catch (jsonError) {
              // Return original string if all decoding fails
              return data;
            }
          }
        }
      } catch (base64Error) {
        // Try direct JSON parse
        try {
          return JSON.parse(data);
        } catch (jsonError) {
          return data;
        }
      }
    } else if (typeof data === 'object') {
      return data;
    }
    
    return data;
  } catch (error) {
    console.warn('Failed to decode nested data:', error.message);
    return data;
  }
}

// User type mapping
const USER_TYPE_MAPPING = {
  'buyer': 'BUYER',
  'farmer': 'FARMER',
  'admin': 'ADMIN',
  'agent': 'AGENT'
};

async function migrateUsers() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    // Connect to both databases
    await mongoClient.connect();
    await pgClient.connect();

    console.log('Connected to MongoDB and PostgreSQL');

    const db = mongoClient.db(MONGODB_DATABASE);

    // Get all collections that might contain user data
    const collections = await db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));

    // Collect users from fragmented collections
    let allUsers = [];

    // Get buyers
    const buyersCollection = db.collection('buyers');
    const buyers = await buyersCollection.find({}).toArray();
    console.log(`Found ${buyers.length} buyers in MongoDB`);

    // Get farmers
    const farmersCollection = db.collection('farmers');
    const farmers = await farmersCollection.find({}).toArray();
    console.log(`Found ${farmers.length} farmers in MongoDB`);

    // Get admins
    const adminsCollection = db.collection('admins');
    const admins = await adminsCollection.find({}).toArray();
    console.log(`Found ${admins.length} admins in MongoDB`);

    // Get profiles, contacts, businesses for additional data
    const profilesCollection = db.collection('profiles');
    const profiles = await profilesCollection.find({}).toArray();
    console.log(`Found ${profiles.length} profiles in MongoDB`);

    const contactsCollection = db.collection('contacts');
    const contacts = await contactsCollection.find({}).toArray();
    console.log(`Found ${contacts.length} contacts in MongoDB`);

    const businessesCollection = db.collection('businesses');
    const businesses = await businessesCollection.find({}).toArray();
    console.log(`Found ${businesses.length} businesses in MongoDB`);

    // Create lookup maps for additional data
    const profilesMap = new Map();
    const contactsMap = new Map();
    const businessesMap = new Map();

    profiles.forEach(p => profilesMap.set(p._id.toString(), p));
    contacts.forEach(c => contactsMap.set(c._id.toString(), c));
    businesses.forEach(b => businessesMap.set(b._id.toString(), b));

    console.log('Profile IDs in map:', Array.from(profilesMap.keys()).slice(0, 5));
    console.log('Contact IDs in map:', Array.from(contactsMap.keys()).slice(0, 5));
    console.log('Business IDs in map:', Array.from(businessesMap.keys()).slice(0, 5));

    // Process buyers
    for (const buyer of buyers) {
      buyer.userType = 'buyer';
      buyer.originalCollection = 'buyers';
      allUsers.push(buyer);
    }

    // Process farmers
    for (const farmer of farmers) {
      farmer.userType = 'farmer';
      farmer.originalCollection = 'farmers';
      allUsers.push(farmer);
    }

    // Process admins
    for (const admin of admins) {
      admin.userType = 'admin';
      admin.originalCollection = 'admins';
      allUsers.push(admin);
    }

    console.log(`Total users to migrate: ${allUsers.length}`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const user of allUsers) {
      try {
        console.log(`\nProcessing user: ${user._id} (${user.email || user.username || 'no-email'}) from ${user.originalCollection}`);

        // Get additional data from lookup maps
        let profile = null;
        let contact = null;
        let business = null;

        // Get related profile data using ObjectId reference
        if (user.profile) {
          const profileId = user.profile.toString();
          profile = profilesMap.get(profileId);
          console.log(`Looking for profile ID: ${profileId}, found:`, profile ? 'yes' : 'no');
        }

        // Get related contact data using ObjectId reference
        if (user.contact) {
          const contactId = user.contact.toString();
          contact = contactsMap.get(contactId);
          console.log(`Looking for contact ID: ${contactId}, found:`, contact ? 'yes' : 'no');
        }

        // Get related business data using ObjectId reference
        if (user.business) {
          const businessId = user.business.toString();
          business = businessesMap.get(businessId);
          console.log(`Looking for business ID: ${businessId}, found:`, business ? 'yes' : 'no');
        }

        console.log('Profile data:', profile ? 'found' : 'not found');
        console.log('Contact data:', contact ? 'found' : 'not found');
        console.log('Business data:', business ? 'found' : 'not found');
        
        // Extract user data with fallbacks
        const userData = {
          id: crypto.randomUUID(),
          username: user.username || user.email || `user_${user._id}`,
          password: user.password || '$2b$10$defaulthashedpassword', // Default hashed password
          user_type: USER_TYPE_MAPPING[user.userType] || USER_TYPE_MAPPING[user.type] || 'BUYER',
          verified: user.verified || false,
          is_active: user.isActive !== undefined ? user.isActive : true,
          last_login: user.lastLogin ? new Date(user.lastLogin) : null,
          
          // Profile fields with fallbacks
          first_name: (profile && profile.firstName) || user.firstName || user.first_name || 'Unknown',
          last_name: (profile && profile.lastName) || user.lastName || user.last_name || 'User',
          middle_name: (profile && profile.middleName) || user.middleName || user.middle_name || null,
          prefix: (profile && profile.prefix) || user.prefix || null,
          gender: (profile && profile.gender) || user.gender || null,
          date_of_birth: (profile && profile.dateOfBirth) || user.dateOfBirth || user.date_of_birth || null,
          profile_picture: (profile && profile.profilePicture) || user.profilePicture || user.profile_picture || null,
          
          // Contact fields with fallbacks
          email: (contact && contact.email) || user.email || `${user._id}@temp.com`,
          primary_phone: (contact && contact.primaryPhone) || (contact && contact.phoneNumber) || user.phoneNumber || user.phone || user.primary_phone || '+1234567890',
          secondary_phone: (contact && contact.secondaryPhone) || user.secondaryPhone || user.secondary_phone || null,
          
          // Status fields
          is_premium: user.isPremium || user.is_premium || false,
          is_phone_verified: user.isPhoneVerified || user.is_phone_verified || false,
          is_email_verified: user.isEmailVerified || user.is_email_verified || false,
          
          // Timestamps
          created_at: user.createdAt || user.created_at || new Date(),
          updated_at: user.updatedAt || user.updated_at || new Date()
        };
        
        // Insert user into PostgreSQL
        const insertUserQuery = `
          INSERT INTO users (
            id, username, password, user_type, verified, is_active, last_login,
            first_name, last_name, middle_name, prefix, gender, date_of_birth, profile_picture,
            email, primary_phone, secondary_phone, is_premium, is_phone_verified, is_email_verified,
            created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
          ) ON CONFLICT (email) DO NOTHING
          RETURNING id
        `;
        
        const userResult = await pgClient.query(insertUserQuery, [
          userData.id, userData.username, userData.password, userData.user_type,
          userData.verified, userData.is_active, userData.last_login,
          userData.first_name, userData.last_name, userData.middle_name, userData.prefix,
          userData.gender, userData.date_of_birth, userData.profile_picture,
          userData.email, userData.primary_phone, userData.secondary_phone,
          userData.is_premium, userData.is_phone_verified, userData.is_email_verified,
          userData.created_at, userData.updated_at
        ]);
        
        if (userResult.rows.length === 0) {
          console.log(`User ${userData.email} already exists, skipping...`);
          continue;
        }
        
        const insertedUserId = userResult.rows[0].id;
        console.log(`Inserted user: ${userData.email} with ID: ${insertedUserId}`);
        
        // Create type-specific profile
        if (userData.user_type === 'FARMER') {
          await createFarmerProfile(pgClient, insertedUserId, user, profile, business);
        } else if (userData.user_type === 'BUYER') {
          await createBuyerProfile(pgClient, insertedUserId, user, business);
        } else if (userData.user_type === 'ADMIN') {
          await createAdminProfile(pgClient, insertedUserId, user);
        }
        
        // Migrate addresses
        await migrateUserAddresses(pgClient, insertedUserId, user, db);
        
        migratedCount++;
        console.log(`Successfully migrated user ${migratedCount}: ${userData.email}`);
        
      } catch (error) {
        errorCount++;
        console.error(`Error migrating user ${user._id}:`, error.message);
        console.error('User data:', JSON.stringify(user, null, 2));
      }
    }
    
    console.log(`\nMigration completed:`);
    console.log(`- Successfully migrated: ${migratedCount} users`);
    console.log(`- Errors: ${errorCount} users`);
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function createFarmerProfile(pgClient, userId, mongoUser, profile, business) {
  try {
    const farmerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      farm_name: (business && business.farmName) || mongoUser.farmName || mongoUser.farm_name || null,
      farm_location: (business && business.farmLocation) || mongoUser.farmLocation || mongoUser.farm_location || null,
      farm_size: (business && business.farmSize) || mongoUser.farmSize || mongoUser.farm_size || null,
      farm_address: (business && business.farmAddress) || mongoUser.farmAddress || mongoUser.farm_address || null,
      account_number: (business && business.accountNumber) || mongoUser.accountNumber || mongoUser.account_number || null,
      account_name: (business && business.accountName) || mongoUser.accountName || mongoUser.account_name || null,
      bank_name: (business && business.bankName) || mongoUser.bankName || mongoUser.bank_name || null,
      bank_branch: (business && business.bankBranch) || mongoUser.bankBranch || mongoUser.bank_branch || null,
      created_at: mongoUser.createdAt || new Date(),
      updated_at: mongoUser.updatedAt || new Date()
    };
    
    const insertFarmerQuery = `
      INSERT INTO farmers (
        id, user_id, farm_name, farm_location, farm_size, farm_address,
        account_number, account_name, bank_name, bank_branch, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `;
    
    await pgClient.query(insertFarmerQuery, [
      farmerData.id, farmerData.user_id, farmerData.farm_name,
      JSON.stringify(farmerData.farm_location), farmerData.farm_size, farmerData.farm_address,
      farmerData.account_number, farmerData.account_name, farmerData.bank_name,
      farmerData.bank_branch, farmerData.created_at, farmerData.updated_at
    ]);
    
    console.log(`Created farmer profile for user: ${userId}`);
    
    // Create wallet for farmer
    await createFarmerWallet(pgClient, farmerData.id, mongoUser);
    
  } catch (error) {
    console.error(`Error creating farmer profile for user ${userId}:`, error.message);
  }
}

async function createBuyerProfile(pgClient, userId, mongoUser, business) {
  try {
    const buyerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      business_name: (business && business.businessName) || mongoUser.businessName || mongoUser.business_name || null,
      loyalty_points: mongoUser.loyaltyPoints || mongoUser.loyalty_points || 0,
      delivery_preferences: mongoUser.deliveryPreferences || mongoUser.delivery_preferences || null,
      payment_methods: mongoUser.paymentMethods || mongoUser.payment_methods || null,
      created_at: mongoUser.createdAt || new Date(),
      updated_at: mongoUser.updatedAt || new Date()
    };
    
    const insertBuyerQuery = `
      INSERT INTO buyers (
        id, user_id, business_name, loyalty_points, delivery_preferences, payment_methods, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `;
    
    await pgClient.query(insertBuyerQuery, [
      buyerData.id, buyerData.user_id, buyerData.business_name, buyerData.loyalty_points,
      JSON.stringify(buyerData.delivery_preferences), JSON.stringify(buyerData.payment_methods),
      buyerData.created_at, buyerData.updated_at
    ]);
    
    console.log(`Created buyer profile for user: ${userId}`);
    
  } catch (error) {
    console.error(`Error creating buyer profile for user ${userId}:`, error.message);
  }
}

async function createAdminProfile(pgClient, userId, mongoUser) {
  try {
    const adminData = {
      id: crypto.randomUUID(),
      user_id: userId,
      role: mongoUser.role || 'SUB_ADMIN',
      permissions: mongoUser.permissions || null,
      managed_departments: mongoUser.managedDepartments || mongoUser.managed_departments || null,
      admin_token: mongoUser.adminToken || mongoUser.admin_token || null,
      created_at: mongoUser.createdAt || new Date(),
      updated_at: mongoUser.updatedAt || new Date()
    };
    
    const insertAdminQuery = `
      INSERT INTO admins (
        id, user_id, role, permissions, managed_departments, admin_token, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `;
    
    await pgClient.query(insertAdminQuery, [
      adminData.id, adminData.user_id, adminData.role,
      JSON.stringify(adminData.permissions), JSON.stringify(adminData.managed_departments),
      adminData.admin_token, adminData.created_at, adminData.updated_at
    ]);
    
    console.log(`Created admin profile for user: ${userId}`);
    
  } catch (error) {
    console.error(`Error creating admin profile for user ${userId}:`, error.message);
  }
}

async function createFarmerWallet(pgClient, farmerId, mongoUser) {
  try {
    const walletData = {
      id: crypto.randomUUID(),
      farmer_id: farmerId,
      balance: mongoUser.wallet?.balance || mongoUser.balance || 0,
      gross_revenue: mongoUser.wallet?.grossRevenue || mongoUser.grossRevenue || mongoUser.gross_revenue || 0,
      created_at: mongoUser.createdAt || new Date(),
      updated_at: mongoUser.updatedAt || new Date()
    };
    
    const insertWalletQuery = `
      INSERT INTO wallets (id, farmer_id, balance, gross_revenue, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `;
    
    await pgClient.query(insertWalletQuery, [
      walletData.id, walletData.farmer_id, walletData.balance,
      walletData.gross_revenue, walletData.created_at, walletData.updated_at
    ]);
    
    console.log(`Created wallet for farmer: ${farmerId}`);
    
  } catch (error) {
    console.error(`Error creating wallet for farmer ${farmerId}:`, error.message);
  }
}

async function migrateUserAddresses(pgClient, userId, mongoUser, db) {
  try {
    const addressIds = mongoUser.addresses || [];

    if (addressIds.length === 0) return;

    // Get addresses from the addresses collection using the IDs
    const addressesCollection = db.collection('addresses');
    const addresses = await addressesCollection.find({
      _id: { $in: addressIds.map(id => typeof id === 'string' ? id : id.toString()) }
    }).toArray();

    console.log(`Found ${addresses.length} addresses for user ${mongoUser.username}`);

    for (const address of addresses) {
      const addressData = {
        id: crypto.randomUUID(),
        user_id: userId,
        house_number: address.houseNumber || address.house_number || 'N/A',
        street_name: address.streetName || address.street_name || address.street || 'N/A',
        community: address.community || 'N/A',
        lga: address.lga || address.localGovernment || 'N/A',
        state: address.state || 'N/A',
        country: address.country || 'Nigeria',
        is_default: address.isDefault || address.is_default || false,
        created_at: address.createdAt || new Date(),
        updated_at: address.updatedAt || new Date()
      };

      const insertAddressQuery = `
        INSERT INTO addresses (
          id, user_id, house_number, street_name, community, lga, state, country, is_default, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `;

      await pgClient.query(insertAddressQuery, [
        addressData.id, addressData.user_id, addressData.house_number, addressData.street_name,
        addressData.community, addressData.lga, addressData.state, addressData.country,
        addressData.is_default, addressData.created_at, addressData.updated_at
      ]);
    }

    console.log(`Migrated ${addresses.length} addresses for user: ${userId}`);

  } catch (error) {
    console.error(`Error migrating addresses for user ${userId}:`, error.message);
  }
}

async function migrateCategories() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();

    const db = mongoClient.db(MONGODB_DATABASE);
    const categoriesCollection = db.collection('categories');
    const categories = await categoriesCollection.find({}).toArray();

    console.log(`\nMigrating ${categories.length} categories...`);

    for (const category of categories) {
      try {
        const categoryData = {
          id: crypto.randomUUID(),
          name: category.name,
          slug: category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
          description: category.description || null,
          image: category.image || null,
          is_active: category.isActive !== undefined ? category.isActive : true,
          created_at: category.createdAt || new Date(),
          updated_at: category.updatedAt || new Date()
        };

        const insertCategoryQuery = `
          INSERT INTO categories (id, name, slug, description, image, is_active, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          ON CONFLICT (name) DO NOTHING
        `;

        await pgClient.query(insertCategoryQuery, [
          categoryData.id, categoryData.name, categoryData.slug, categoryData.description,
          categoryData.image, categoryData.is_active, categoryData.created_at, categoryData.updated_at
        ]);

        console.log(`Migrated category: ${categoryData.name}`);

      } catch (error) {
        console.error(`Error migrating category ${category._id}:`, error.message);
      }
    }

  } catch (error) {
    console.error('Category migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function runFullMigration() {
  console.log('Starting full data migration from MongoDB to PostgreSQL...\n');

  try {
    // Step 1: Migrate categories first (needed for produces)
    console.log('=== STEP 1: Migrating Categories ===');
    await migrateCategories();

    // Step 2: Migrate users and their profiles
    console.log('\n=== STEP 2: Migrating Users ===');
    await migrateUsers();

    console.log('\n=== MIGRATION COMPLETED ===');
    console.log('All data has been successfully migrated from MongoDB to PostgreSQL');

  } catch (error) {
    console.error('Full migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runFullMigration().catch(console.error);
}

module.exports = { migrateUsers, migrateCategories, runFullMigration, decodeNestedData };
