const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');

const MONGODB_URI = 'mongodb+srv://eweko:<EMAIL>/?retryWrites=true&w=majority&appName=ewekodev';
const MONGODB_DATABASE = 'ewekodev';

const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

async function checkForAdditionalUsers() {
  const mongoClient = new MongoClient(MONGODB_URI);
  
  try {
    await mongoClient.connect();
    const db = mongoClient.db(MONGODB_DATABASE);
    
    console.log('=== CHECKING FOR ADDITIONAL USERS ===');
    
    // Check if there are any users in the main users collection
    const usersCollection = db.collection('users');
    const mainUsers = await usersCollection.find({}).toArray();
    console.log(`Users in main 'users' collection: ${mainUsers.length}`);
    
    if (mainUsers.length > 0) {
      console.log('Sample user from main collection:');
      console.log(JSON.stringify(mainUsers[0], null, 2));
    }
    
    // Check agents collection
    const agentsCollection = db.collection('agents');
    const agents = await agentsCollection.find({}).toArray();
    console.log(`Users in 'agents' collection: ${agents.length}`);
    
    if (agents.length > 0) {
      console.log('Sample agent:');
      console.log(JSON.stringify(agents[0], null, 2));
    }
    
    // Check fieldagents collection
    const fieldAgentsCollection = db.collection('fieldagents');
    const fieldAgents = await fieldAgentsCollection.find({}).toArray();
    console.log(`Users in 'fieldagents' collection: ${fieldAgents.length}`);
    
    if (fieldAgents.length > 0) {
      console.log('Sample field agent:');
      console.log(JSON.stringify(fieldAgents[0], null, 2));
    }
    
    // Check for any other collections that might contain user data
    const collections = await db.listCollections().toArray();
    const userRelatedCollections = collections.filter(c => 
      c.name.toLowerCase().includes('user') || 
      c.name.toLowerCase().includes('admin') ||
      c.name.toLowerCase().includes('agent')
    );
    
    console.log('User-related collections:', userRelatedCollections.map(c => c.name));
    
  } catch (error) {
    console.error('Error checking for additional users:', error);
  } finally {
    await mongoClient.close();
  }
}

async function migrateProduces() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('\n=== MIGRATING PRODUCES ===');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    const producesCollection = db.collection('produces');
    const produces = await producesCollection.find({}).toArray();
    
    console.log(`Found ${produces.length} produces to migrate`);
    
    // Get farmer and category mappings from PostgreSQL
    const farmersResult = await pgClient.query('SELECT id, user_id FROM farmers');
    const categoriesResult = await pgClient.query('SELECT id, name FROM categories');
    
    const farmerMap = new Map();
    farmersResult.rows.forEach(f => farmerMap.set(f.user_id, f.id));
    
    const categoryMap = new Map();
    categoriesResult.rows.forEach(c => categoryMap.set(c.name.toLowerCase(), c.id));
    
    let migratedCount = 0;
    
    for (const produce of produces) {
      try {
        // Find the farmer ID
        const farmerId = farmerMap.get(produce.farmer?.toString()) || farmerMap.get(produce.farmerId?.toString());
        
        if (!farmerId) {
          console.log(`Skipping produce ${produce.name} - farmer not found`);
          continue;
        }
        
        // Find or create category
        let categoryId = categoryMap.get(produce.category?.toLowerCase());
        if (!categoryId) {
          // Create a default category if not found
          const defaultCategoryResult = await pgClient.query(
            'INSERT INTO categories (id, name, slug, is_active, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id',
            [crypto.randomUUID(), produce.category || 'Other', (produce.category || 'other').toLowerCase().replace(/\s+/g, '-'), true, new Date(), new Date()]
          );
          categoryId = defaultCategoryResult.rows[0].id;
          categoryMap.set((produce.category || 'other').toLowerCase(), categoryId);
        }
        
        const produceData = {
          id: crypto.randomUUID(),
          farmer_id: farmerId,
          category_id: categoryId,
          name: produce.name || 'Unknown Produce',
          description: produce.description || null,
          price: produce.price || 0,
          negotiable_price: produce.negotiablePrice || produce.negotiable_price || produce.price || 0,
          min_order_qty: produce.minOrderQty || produce.min_order_qty || 1,
          stock: produce.stock || 0,
          images: JSON.stringify(produce.images || []),
          slug: produce.slug || produce.name?.toLowerCase().replace(/\s+/g, '-') || 'unknown-produce',
          harvest_date: produce.harvestDate || produce.harvest_date || null,
          created_at: produce.createdAt || new Date(),
          updated_at: produce.updatedAt || new Date()
        };
        
        const insertProduceQuery = `
          INSERT INTO produces (
            id, farmer_id, category_id, name, description, price, negotiable_price,
            min_order_qty, stock, images, slug, harvest_date, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          ON CONFLICT (slug) DO NOTHING
        `;
        
        await pgClient.query(insertProduceQuery, [
          produceData.id, produceData.farmer_id, produceData.category_id, produceData.name,
          produceData.description, produceData.price, produceData.negotiable_price,
          produceData.min_order_qty, produceData.stock, produceData.images,
          produceData.slug, produceData.harvest_date, produceData.created_at, produceData.updated_at
        ]);
        
        migratedCount++;
        console.log(`Migrated produce: ${produceData.name}`);
        
      } catch (error) {
        console.error(`Error migrating produce ${produce._id}:`, error.message);
      }
    }
    
    console.log(`Successfully migrated ${migratedCount} produces`);
    
  } catch (error) {
    console.error('Produce migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function runComprehensiveMigration() {
  console.log('Starting comprehensive migration check...\n');
  
  try {
    // Step 1: Check for any additional users we might have missed
    await checkForAdditionalUsers();
    
    // Step 2: Migrate produces
    await migrateProduces();
    
    console.log('\n=== COMPREHENSIVE MIGRATION CHECK COMPLETED ===');
    
  } catch (error) {
    console.error('Comprehensive migration failed:', error);
    process.exit(1);
  }
}

// Run the comprehensive migration
if (require.main === module) {
  runComprehensiveMigration().catch(console.error);
}

module.exports = { checkForAdditionalUsers, migrateProduces, runComprehensiveMigration };
