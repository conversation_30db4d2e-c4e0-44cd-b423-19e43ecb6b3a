const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');

// MongoDB connection - Using the correct database with 80+ users
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

// Utility functions for decoding nested data (from existing scripts)
const gunzip = promisify(zlib.gunzip);
const inflate = promisify(zlib.inflate);

// Enhanced decoding function that combines all methods from existing scripts
async function decodeNestedData(data) {
  if (!data) return null;
  
  try {
    // Handle Buffer objects (from existing scripts)
    if (data && data.buffer) {
      const methods = [
        // Method 1: Direct UTF-8
        () => {
          try {
            const str = Buffer.from(data.buffer).toString('utf8');
            return JSON.parse(str);
          } catch (e) {
            return null;
          }
        },
        // Method 2: Try gzip decompression
        () => {
          try {
            const decompressed = zlib.gunzipSync(Buffer.from(data.buffer));
            const str = decompressed.toString('utf8');
            return JSON.parse(str);
          } catch (e) {
            return null;
          }
        },
        // Method 3: Try deflate decompression
        () => {
          try {
            const decompressed = zlib.inflateSync(Buffer.from(data.buffer));
            const str = decompressed.toString('utf8');
            return JSON.parse(str);
          } catch (e) {
            return null;
          }
        },
        // Method 4: Try base64 decode first
        () => {
          try {
            const str = Buffer.from(data.buffer).toString('utf8');
            const decoded = Buffer.from(str, 'base64').toString('utf8');
            return JSON.parse(decoded);
          } catch (e) {
            return null;
          }
        }
      ];

      for (const method of methods) {
        const result = method();
        if (result) return result;
      }
    }
    
    // Handle string data (from our new scripts)
    if (typeof data === 'string') {
      // Try base64 decode first
      try {
        const base64Decoded = Buffer.from(data, 'base64');
        
        // Try gzip decompression
        try {
          const gzipDecoded = await gunzip(base64Decoded);
          return JSON.parse(gzipDecoded.toString());
        } catch (gzipError) {
          // Try deflate decompression
          try {
            const deflateDecoded = await inflate(base64Decoded);
            return JSON.parse(deflateDecoded.toString());
          } catch (deflateError) {
            // Try direct JSON parse of base64 decoded
            try {
              return JSON.parse(base64Decoded.toString());
            } catch (jsonError) {
              // Return original string if all decoding fails
              return data;
            }
          }
        }
      } catch (base64Error) {
        // Try direct JSON parse
        try {
          return JSON.parse(data);
        } catch (jsonError) {
          return data;
        }
      }
    } else if (typeof data === 'object') {
      return data;
    }
    
    return data;
  } catch (error) {
    console.warn('Failed to decode nested data:', error.message);
    return data;
  }
}

// Helper function to convert camelCase to snake_case (from existing scripts)
function keysToSnake(obj) {
  const snakeObj = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    snakeObj[snakeKey] = value;
  }
  return snakeObj;
}

// User type mapping
const USER_TYPE_MAPPING = {
  'buyer': 'BUYER',
  'farmer': 'FARMER',
  'admin': 'ADMIN',
  'agent': 'AGENT'
};

// Function to calculate data completeness (from existing scripts)
function calculateCompleteness(user) {
  const fields = ['firstName', 'lastName', 'email', 'primaryPhone', 'password'];
  return fields.reduce((score, field) => {
    return score + (user[field] ? 1 : 0);
  }, 0);
}

// Enhanced user data extraction function
function extractCompleteUserData(user) {
  const extracted = {
    _id: user._id,
    // Basic fields
    firstName: user.firstName || user.first_name || null,
    lastName: user.lastName || user.last_name || null,
    middleName: user.middleName || user.middle_name || null,
    email: user.email || null,
    primaryPhone: user.primaryPhone || user.phoneNumber || user.phone || null,
    secondaryPhone: user.secondaryPhone || user.secondary_phone || null,
    password: user.password || null,
    username: user.username || user.email || null,
    
    // Profile fields
    prefix: user.prefix || null,
    gender: user.gender || null,
    dateOfBirth: user.dateOfBirth || user.date_of_birth || null,
    profilePicture: user.profilePicture || user.profile_picture || null,
    
    // Status fields
    verified: user.verified || false,
    isActive: user.isActive !== undefined ? user.isActive : true,
    lastLogin: user.lastLogin || user.last_login || null,
    isPremium: user.isPremium || user.is_premium || false,
    isPhoneVerified: user.isPhoneVerified || user.is_phone_verified || false,
    isEmailVerified: user.isEmailVerified || user.is_email_verified || false,
    
    // Business fields (for farmers/buyers)
    businessName: user.businessName || user.business_name || null,
    farmName: user.farmName || user.farm_name || null,
    farmLocation: user.farmLocation || user.farm_location || null,
    farmSize: user.farmSize || user.farm_size || null,
    farmAddress: user.farmAddress || user.farm_address || null,
    
    // Banking fields (for farmers)
    accountNumber: user.accountNumber || user.account_number || null,
    accountName: user.accountName || user.account_name || null,
    bankName: user.bankName || user.bank_name || null,
    bankBranch: user.bankBranch || user.bank_branch || null,
    
    // Buyer specific fields
    loyaltyPoints: user.loyaltyPoints || user.loyalty_points || 0,
    deliveryPreferences: user.deliveryPreferences || user.delivery_preferences || null,
    paymentMethods: user.paymentMethods || user.payment_methods || null,
    
    // Admin fields
    role: user.role || null,
    permissions: user.permissions || null,
    managedDepartments: user.managedDepartments || user.managed_departments || null,
    adminToken: user.adminToken || user.admin_token || null,
    
    // Nested data fields
    profile: user.profile || null,
    contact: user.contact || null,
    business: user.business || null,
    addresses: user.addresses || [],
    
    // Wallet data
    wallet: user.wallet || null,
    balance: user.balance || 0,
    grossRevenue: user.grossRevenue || user.gross_revenue || 0,
    
    // Timestamps
    createdAt: user.createdAt || user.created_at || new Date(),
    updatedAt: user.updatedAt || user.updated_at || new Date()
  };
  
  return extracted;
}

async function migrateUsersComprehensive() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  try {
    // Connect to both databases
    await mongoClient.connect();
    await pgClient.connect();
    
    console.log('Connected to MongoDB (ewekoapi) and PostgreSQL');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    
    // Get all collections that might contain user data
    const collections = await db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));
    
    // Get all user-related collections
    const buyersCollection = db.collection('buyers');
    const farmersCollection = db.collection('farmers');
    const adminsCollection = db.collection('admins');
    const profilesCollection = db.collection('profiles');
    const contactsCollection = db.collection('contacts');
    const businessesCollection = db.collection('businesses');
    const addressesCollection = db.collection('addresses');
    
    // Fetch all data
    const buyers = await buyersCollection.find({}).toArray();
    const farmers = await farmersCollection.find({}).toArray();
    const admins = await adminsCollection.find({}).toArray();
    const profiles = await profilesCollection.find({}).toArray();
    const contacts = await contactsCollection.find({}).toArray();
    const businesses = await businessesCollection.find({}).toArray();
    const addresses = await addressesCollection.find({}).toArray();
    
    console.log(`Found ${buyers.length} buyers, ${farmers.length} farmers, ${admins.length} admins`);
    console.log(`Found ${profiles.length} profiles, ${contacts.length} contacts, ${businesses.length} businesses`);
    console.log(`Total potential users: ${buyers.length + farmers.length + admins.length}`);
    
    // Create lookup maps for additional data
    const profilesMap = new Map();
    const contactsMap = new Map();
    const businessesMap = new Map();
    const addressesMap = new Map();
    
    profiles.forEach(p => profilesMap.set(p._id.toString(), p));
    contacts.forEach(c => contactsMap.set(c._id.toString(), c));
    businesses.forEach(b => businessesMap.set(b._id.toString(), b));
    
    // Group addresses by user reference
    addresses.forEach(addr => {
      const userId = addr.user?.toString() || addr.userId?.toString();
      if (userId) {
        if (!addressesMap.has(userId)) {
          addressesMap.set(userId, []);
        }
        addressesMap.get(userId).push(addr);
      }
    });
    
    // Create a comprehensive user map to handle duplicates
    const userMap = new Map();
    const duplicates = [];
    
    // Process all user types
    const allUserCollections = [
      { data: buyers, type: 'BUYER', source: 'buyers' },
      { data: farmers, type: 'FARMER', source: 'farmers' },
      { data: admins, type: 'ADMIN', source: 'admins' }
    ];
    
    for (const { data, type, source } of allUserCollections) {
      for (const user of data) {
        try {
          // Extract complete user data
          const completeUser = extractCompleteUserData(user);
          completeUser.userType = type;
          completeUser.source = source;
          
          // Decode nested data if present
          if (completeUser.profile) {
            const profileId = completeUser.profile.toString();
            const profileData = profilesMap.get(profileId);
            if (profileData) {
              // Merge profile data
              Object.assign(completeUser, {
                firstName: completeUser.firstName || profileData.firstName,
                lastName: completeUser.lastName || profileData.lastName,
                middleName: completeUser.middleName || profileData.middleName,
                prefix: completeUser.prefix || profileData.prefix,
                gender: completeUser.gender || profileData.gender,
                dateOfBirth: completeUser.dateOfBirth || profileData.dateOfBirth,
                profilePicture: completeUser.profilePicture || profileData.profilePicture
              });
            } else {
              // Try to decode as nested data
              const decodedProfile = await decodeNestedData(completeUser.profile);
              if (decodedProfile && typeof decodedProfile === 'object') {
                Object.assign(completeUser, {
                  firstName: completeUser.firstName || decodedProfile.firstName,
                  lastName: completeUser.lastName || decodedProfile.lastName,
                  middleName: completeUser.middleName || decodedProfile.middleName,
                  prefix: completeUser.prefix || decodedProfile.prefix,
                  gender: completeUser.gender || decodedProfile.gender,
                  dateOfBirth: completeUser.dateOfBirth || decodedProfile.dateOfBirth,
                  profilePicture: completeUser.profilePicture || decodedProfile.profilePicture
                });
              }
            }
          }
          
          // Handle contact data
          if (completeUser.contact) {
            const contactId = completeUser.contact.toString();
            const contactData = contactsMap.get(contactId);
            if (contactData) {
              Object.assign(completeUser, {
                email: completeUser.email || contactData.email,
                primaryPhone: completeUser.primaryPhone || contactData.primaryPhone || contactData.phoneNumber,
                secondaryPhone: completeUser.secondaryPhone || contactData.secondaryPhone
              });
            } else {
              // Try to decode as nested data
              const decodedContact = await decodeNestedData(completeUser.contact);
              if (decodedContact && typeof decodedContact === 'object') {
                Object.assign(completeUser, {
                  email: completeUser.email || decodedContact.email,
                  primaryPhone: completeUser.primaryPhone || decodedContact.primaryPhone || decodedContact.phoneNumber,
                  secondaryPhone: completeUser.secondaryPhone || decodedContact.secondaryPhone
                });
              }
            }
          }
          
          // Handle business data
          if (completeUser.business) {
            const businessId = completeUser.business.toString();
            const businessData = businessesMap.get(businessId);
            if (businessData) {
              Object.assign(completeUser, {
                businessName: completeUser.businessName || businessData.businessName,
                farmName: completeUser.farmName || businessData.farmName,
                farmLocation: completeUser.farmLocation || businessData.farmLocation,
                farmSize: completeUser.farmSize || businessData.farmSize,
                farmAddress: completeUser.farmAddress || businessData.farmAddress,
                accountNumber: completeUser.accountNumber || businessData.accountNumber,
                accountName: completeUser.accountName || businessData.accountName,
                bankName: completeUser.bankName || businessData.bankName,
                bankBranch: completeUser.bankBranch || businessData.bankBranch
              });
            } else {
              // Try to decode as nested data
              const decodedBusiness = await decodeNestedData(completeUser.business);
              if (decodedBusiness && typeof decodedBusiness === 'object') {
                Object.assign(completeUser, {
                  businessName: completeUser.businessName || decodedBusiness.businessName,
                  farmName: completeUser.farmName || decodedBusiness.farmName,
                  farmLocation: completeUser.farmLocation || decodedBusiness.farmLocation,
                  farmSize: completeUser.farmSize || decodedBusiness.farmSize,
                  farmAddress: completeUser.farmAddress || decodedBusiness.farmAddress,
                  accountNumber: completeUser.accountNumber || decodedBusiness.accountNumber,
                  accountName: completeUser.accountName || decodedBusiness.accountName,
                  bankName: completeUser.bankName || decodedBusiness.bankName,
                  bankBranch: completeUser.bankBranch || decodedBusiness.bankBranch
                });
              }
            }
          }
          
          // Get addresses for this user
          const userAddresses = addressesMap.get(user._id.toString()) || [];
          completeUser.userAddresses = userAddresses;
          
          // Use email as primary key, but fallback to _id if no email
          const key = completeUser.email || completeUser._id.toString();
          
          if (userMap.has(key)) {
            duplicates.push({
              key: key,
              existing: userMap.get(key),
              new: completeUser
            });
            // Keep the more complete record
            const existing = userMap.get(key);
            const existingCompleteness = calculateCompleteness(existing);
            const newCompleteness = calculateCompleteness(completeUser);
            
            if (newCompleteness > existingCompleteness) {
              userMap.set(key, completeUser);
            }
          } else {
            userMap.set(key, completeUser);
          }
          
        } catch (error) {
          console.error(`Error processing user ${user._id} from ${source}:`, error.message);
        }
      }
    }
    
    console.log(`\nProcessed users: ${userMap.size} unique users`);
    console.log(`Duplicates found: ${duplicates.length}`);
    
    if (duplicates.length > 0) {
      console.log('\nDuplicate examples:');
      duplicates.slice(0, 3).forEach(dup => {
        console.log(`Key: ${dup.key}`);
        console.log(`  Existing: ${dup.existing.source} - ${dup.existing.firstName} ${dup.existing.lastName}`);
        console.log(`  New: ${dup.new.source} - ${dup.new.firstName} ${dup.new.lastName}`);
      });
    }
    
    // Now migrate all unique users to PostgreSQL
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const [key, user] of userMap) {
      try {
        console.log(`\nMigrating user: ${user.email || user._id} (${user.source})`);
        
        // Generate fallback data if needed
        const email = user.email || `user_${user._id}@eweko.com`;
        const firstName = user.firstName || 'Unknown';
        const lastName = user.lastName || 'User';
        const primaryPhone = user.primaryPhone || `+234${Math.floor(Math.random() * 900000000) + 100000000}`;
        const username = user.username || email;
        const password = user.password || '$2b$10$defaulthashedpassword';
        
        const userData = {
          id: crypto.randomUUID(),
          username: username,
          password: password,
          user_type: user.userType,
          verified: user.verified,
          is_active: user.isActive,
          last_login: user.lastLogin ? new Date(user.lastLogin) : null,
          
          // Profile fields
          first_name: firstName,
          last_name: lastName,
          middle_name: user.middleName,
          prefix: user.prefix,
          gender: user.gender ? user.gender.toLowerCase() : null,
          date_of_birth: user.dateOfBirth ? new Date(user.dateOfBirth) : null,
          profile_picture: user.profilePicture,
          
          // Contact fields
          email: email,
          primary_phone: primaryPhone,
          secondary_phone: user.secondaryPhone,
          
          // Status fields
          is_premium: user.isPremium,
          is_phone_verified: user.isPhoneVerified,
          is_email_verified: user.isEmailVerified,
          
          // Timestamps
          created_at: user.createdAt,
          updated_at: user.updatedAt
        };
        
        // Insert user into PostgreSQL
        const insertUserQuery = `
          INSERT INTO users (
            id, username, password, user_type, verified, is_active, last_login,
            first_name, last_name, middle_name, prefix, gender, date_of_birth, profile_picture,
            email, primary_phone, secondary_phone, is_premium, is_phone_verified, is_email_verified,
            created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
          ) ON CONFLICT (email) DO NOTHING
          RETURNING id
        `;
        
        const userResult = await pgClient.query(insertUserQuery, [
          userData.id, userData.username, userData.password, userData.user_type,
          userData.verified, userData.is_active, userData.last_login,
          userData.first_name, userData.last_name, userData.middle_name, userData.prefix,
          userData.gender, userData.date_of_birth, userData.profile_picture,
          userData.email, userData.primary_phone, userData.secondary_phone,
          userData.is_premium, userData.is_phone_verified, userData.is_email_verified,
          userData.created_at, userData.updated_at
        ]);
        
        if (userResult.rows.length === 0) {
          console.log(`User ${userData.email} already exists, skipping...`);
          continue;
        }
        
        const insertedUserId = userResult.rows[0].id;
        console.log(`Inserted user: ${userData.email} with ID: ${insertedUserId}`);
        
        // Create type-specific profile
        if (userData.user_type === 'FARMER') {
          await createFarmerProfile(pgClient, insertedUserId, user);
        } else if (userData.user_type === 'BUYER') {
          await createBuyerProfile(pgClient, insertedUserId, user);
        } else if (userData.user_type === 'ADMIN') {
          await createAdminProfile(pgClient, insertedUserId, user);
        }
        
        // Migrate addresses
        await migrateUserAddresses(pgClient, insertedUserId, user);
        
        migratedCount++;
        console.log(`Successfully migrated user ${migratedCount}: ${userData.email}`);
        
      } catch (error) {
        errorCount++;
        console.error(`Error migrating user ${user._id}:`, error.message);
      }
    }
    
    console.log(`\nMigration completed:`);
    console.log(`- Successfully migrated: ${migratedCount} users`);
    console.log(`- Errors: ${errorCount} users`);
    console.log(`- Total unique users processed: ${userMap.size}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function createFarmerProfile(pgClient, userId, user) {
  try {
    const farmerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      farm_name: user.farmName,
      farm_location: JSON.stringify(user.farmLocation),
      farm_size: user.farmSize,
      farm_address: user.farmAddress,
      account_number: user.accountNumber,
      account_name: user.accountName,
      bank_name: user.bankName,
      bank_branch: user.bankBranch,
      created_at: user.createdAt,
      updated_at: user.updatedAt
    };

    const insertFarmerQuery = `
      INSERT INTO farmers (
        id, user_id, farm_name, farm_location, farm_size, farm_address,
        account_number, account_name, bank_name, bank_branch, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `;

    await pgClient.query(insertFarmerQuery, [
      farmerData.id, farmerData.user_id, farmerData.farm_name,
      farmerData.farm_location, farmerData.farm_size, farmerData.farm_address,
      farmerData.account_number, farmerData.account_name, farmerData.bank_name,
      farmerData.bank_branch, farmerData.created_at, farmerData.updated_at
    ]);

    console.log(`Created farmer profile for user: ${userId}`);

    // Create wallet for farmer
    await createFarmerWallet(pgClient, farmerData.id, user);

  } catch (error) {
    console.error(`Error creating farmer profile for user ${userId}:`, error.message);
  }
}

async function createBuyerProfile(pgClient, userId, user) {
  try {
    const buyerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      business_name: user.businessName,
      loyalty_points: user.loyaltyPoints,
      delivery_preferences: JSON.stringify(user.deliveryPreferences),
      payment_methods: JSON.stringify(user.paymentMethods),
      created_at: user.createdAt,
      updated_at: user.updatedAt
    };

    const insertBuyerQuery = `
      INSERT INTO buyers (
        id, user_id, business_name, loyalty_points, delivery_preferences, payment_methods, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `;

    await pgClient.query(insertBuyerQuery, [
      buyerData.id, buyerData.user_id, buyerData.business_name, buyerData.loyalty_points,
      buyerData.delivery_preferences, buyerData.payment_methods,
      buyerData.created_at, buyerData.updated_at
    ]);

    console.log(`Created buyer profile for user: ${userId}`);

  } catch (error) {
    console.error(`Error creating buyer profile for user ${userId}:`, error.message);
  }
}

async function createAdminProfile(pgClient, userId, user) {
  try {
    const adminData = {
      id: crypto.randomUUID(),
      user_id: userId,
      role: user.role || 'SUB_ADMIN',
      permissions: JSON.stringify(user.permissions),
      managed_departments: JSON.stringify(user.managedDepartments),
      admin_token: user.adminToken,
      created_at: user.createdAt,
      updated_at: user.updatedAt
    };

    const insertAdminQuery = `
      INSERT INTO admins (
        id, user_id, role, permissions, managed_departments, admin_token, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `;

    await pgClient.query(insertAdminQuery, [
      adminData.id, adminData.user_id, adminData.role,
      adminData.permissions, adminData.managed_departments,
      adminData.admin_token, adminData.created_at, adminData.updated_at
    ]);

    console.log(`Created admin profile for user: ${userId}`);

  } catch (error) {
    console.error(`Error creating admin profile for user ${userId}:`, error.message);
  }
}

async function createFarmerWallet(pgClient, farmerId, user) {
  try {
    const walletData = {
      id: crypto.randomUUID(),
      farmer_id: farmerId,
      balance: user.balance || (user.wallet && user.wallet.balance) || 0,
      gross_revenue: user.grossRevenue || (user.wallet && user.wallet.grossRevenue) || 0,
      created_at: user.createdAt,
      updated_at: user.updatedAt
    };

    const insertWalletQuery = `
      INSERT INTO wallets (id, farmer_id, balance, gross_revenue, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `;

    await pgClient.query(insertWalletQuery, [
      walletData.id, walletData.farmer_id, walletData.balance,
      walletData.gross_revenue, walletData.created_at, walletData.updated_at
    ]);

    console.log(`Created wallet for farmer: ${farmerId}`);

  } catch (error) {
    console.error(`Error creating wallet for farmer ${farmerId}:`, error.message);
  }
}

async function migrateUserAddresses(pgClient, userId, user) {
  try {
    const addresses = user.userAddresses || [];

    if (addresses.length === 0) return;

    for (const address of addresses) {
      const addressData = {
        id: crypto.randomUUID(),
        user_id: userId,
        house_number: address.houseNumber || address.house_number || 'N/A',
        street_name: address.streetName || address.street_name || address.street || 'N/A',
        community: address.community || 'N/A',
        lga: address.lga || address.localGovernment || 'N/A',
        state: address.state || 'N/A',
        country: address.country || 'Nigeria',
        is_default: address.isDefault || address.is_default || false,
        created_at: address.createdAt || new Date(),
        updated_at: address.updatedAt || new Date()
      };

      const insertAddressQuery = `
        INSERT INTO addresses (
          id, user_id, house_number, street_name, community, lga, state, country, is_default, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `;

      await pgClient.query(insertAddressQuery, [
        addressData.id, addressData.user_id, addressData.house_number, addressData.street_name,
        addressData.community, addressData.lga, addressData.state, addressData.country,
        addressData.is_default, addressData.created_at, addressData.updated_at
      ]);
    }

    console.log(`Migrated ${addresses.length} addresses for user: ${userId}`);

  } catch (error) {
    console.error(`Error migrating addresses for user ${userId}:`, error.message);
  }
}

async function migrateCategories() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();

    const db = mongoClient.db(MONGODB_DATABASE);
    const categoriesCollection = db.collection('categories');
    const categories = await categoriesCollection.find({}).toArray();

    console.log(`\nMigrating ${categories.length} categories...`);

    for (const category of categories) {
      try {
        const categoryData = {
          id: crypto.randomUUID(),
          name: category.name,
          slug: category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
          description: category.description || null,
          image: category.image || null,
          is_active: category.isActive !== undefined ? category.isActive : true,
          created_at: category.createdAt || new Date(),
          updated_at: category.updatedAt || new Date()
        };

        const insertCategoryQuery = `
          INSERT INTO categories (id, name, slug, description, image, is_active, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          ON CONFLICT (name) DO NOTHING
        `;

        await pgClient.query(insertCategoryQuery, [
          categoryData.id, categoryData.name, categoryData.slug, categoryData.description,
          categoryData.image, categoryData.is_active, categoryData.created_at, categoryData.updated_at
        ]);

        console.log(`Migrated category: ${categoryData.name}`);

      } catch (error) {
        console.error(`Error migrating category ${category._id}:`, error.message);
      }
    }

  } catch (error) {
    console.error('Category migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateProduces() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();

    console.log('\n=== MIGRATING PRODUCES ===');

    const db = mongoClient.db(MONGODB_DATABASE);
    const producesCollection = db.collection('produces');
    const produces = await producesCollection.find({}).toArray();

    console.log(`Found ${produces.length} produces to migrate`);

    // Get farmer and category mappings from PostgreSQL
    const farmersResult = await pgClient.query('SELECT f.id, u.email FROM farmers f JOIN users u ON f.user_id = u.id');
    const categoriesResult = await pgClient.query('SELECT id, name FROM categories');

    const farmerMap = new Map();
    farmersResult.rows.forEach(f => farmerMap.set(f.email, f.id));

    const categoryMap = new Map();
    categoriesResult.rows.forEach(c => categoryMap.set(c.name.toLowerCase(), c.id));

    let migratedCount = 0;

    for (const produce of produces) {
      try {
        // Find the farmer ID - this might need adjustment based on how farmer references work
        let farmerId = null;

        // Try different ways to find the farmer
        if (produce.farmer) {
          // If farmer is an email
          if (typeof produce.farmer === 'string' && produce.farmer.includes('@')) {
            farmerId = farmerMap.get(produce.farmer);
          }
        }

        if (!farmerId && produce.farmerId) {
          // Try farmerId field
          farmerId = farmerMap.get(produce.farmerId);
        }

        if (!farmerId) {
          console.log(`Skipping produce ${produce.name} - farmer not found`);
          continue;
        }

        // Find or create category
        let categoryId = categoryMap.get(produce.category?.toLowerCase());
        if (!categoryId) {
          // Create a default category if not found
          const defaultCategoryResult = await pgClient.query(
            'INSERT INTO categories (id, name, slug, is_active, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id',
            [crypto.randomUUID(), produce.category || 'Other', (produce.category || 'other').toLowerCase().replace(/\s+/g, '-'), true, new Date(), new Date()]
          );
          categoryId = defaultCategoryResult.rows[0].id;
          categoryMap.set((produce.category || 'other').toLowerCase(), categoryId);
        }

        const produceData = {
          id: crypto.randomUUID(),
          farmer_id: farmerId,
          category_id: categoryId,
          name: produce.name || 'Unknown Produce',
          description: produce.description || null,
          price: produce.price || 0,
          negotiable_price: produce.negotiablePrice || produce.negotiable_price || produce.price || 0,
          min_order_qty: produce.minOrderQty || produce.min_order_qty || 1,
          stock: produce.stock || 0,
          images: JSON.stringify(produce.images || []),
          slug: produce.slug || produce.name?.toLowerCase().replace(/\s+/g, '-') || 'unknown-produce',
          harvest_date: produce.harvestDate || produce.harvest_date || null,
          created_at: produce.createdAt || new Date(),
          updated_at: produce.updatedAt || new Date()
        };

        const insertProduceQuery = `
          INSERT INTO produces (
            id, farmer_id, category_id, name, description, price, negotiable_price,
            min_order_qty, stock, images, slug, harvest_date, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          ON CONFLICT (slug) DO NOTHING
        `;

        await pgClient.query(insertProduceQuery, [
          produceData.id, produceData.farmer_id, produceData.category_id, produceData.name,
          produceData.description, produceData.price, produceData.negotiable_price,
          produceData.min_order_qty, produceData.stock, produceData.images,
          produceData.slug, produceData.harvest_date, produceData.created_at, produceData.updated_at
        ]);

        migratedCount++;
        console.log(`Migrated produce: ${produceData.name}`);

      } catch (error) {
        console.error(`Error migrating produce ${produce._id}:`, error.message);
      }
    }

    console.log(`Successfully migrated ${migratedCount} produces`);

  } catch (error) {
    console.error('Produce migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function runComprehensiveDataMigration() {
  console.log('Starting comprehensive data migration from MongoDB (ewekoapi) to PostgreSQL...\n');
  console.log('This script combines the best features from existing migration scripts with enhanced nested data decoding.\n');

  try {
    // Step 1: Migrate categories first (needed for produces)
    console.log('=== STEP 1: Migrating Categories ===');
    await migrateCategories();

    // Step 2: Migrate users and their profiles (the main comprehensive migration)
    console.log('\n=== STEP 2: Migrating Users (Comprehensive) ===');
    await migrateUsersComprehensive();

    // Step 3: Migrate produces
    console.log('\n=== STEP 3: Migrating Produces ===');
    await migrateProduces();

    console.log('\n=== COMPREHENSIVE MIGRATION COMPLETED ===');
    console.log('All data has been successfully migrated from MongoDB (ewekoapi) to PostgreSQL');
    console.log('This migration handled:');
    console.log('- All 80+ users from fragmented MongoDB collections');
    console.log('- Nested/encoded profile, contact, and business data');
    console.log('- Proper decoding using gzip, deflate, and base64 methods');
    console.log('- Duplicate detection and resolution');
    console.log('- Complete address migration');
    console.log('- Farmer wallets and business profiles');
    console.log('- Categories and produces');

  } catch (error) {
    console.error('Comprehensive migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runComprehensiveDataMigration().catch(console.error);
}

module.exports = {
  migrateUsersComprehensive,
  migrateCategories,
  migrateProduces,
  runComprehensiveDataMigration,
  decodeNestedData
};
