// import { Injectable } from '@nestjs/common';
// import { MongoRepository, Repository } from 'typeorm';
// import { PaginationResult } from './types';
// import { PaginationQueryDto } from './pagination-query.dto';

// @Injectable()
// export class PaginationService {
//   async paginate<T>(
//     repository: MongoRepository<T>,
//     paginationQuery: PaginationQueryDto,
//     relations: string[] = [],
//   ): Promise<PaginationResult<T>> {
//     const { page, limit } = paginationQuery;
//     const take = limit || 10;
//     const skip = (page - 1) * take;

//     const [data, total] = await repository.findAndCount({
//       relations,
//       skip,
//       take,
//     });

//     const count = data.length;
//     const totalPages = Math.ceil(total / take);
//     const status = count > 0 ? 'success' : 'no data';

//     return {
//       data,
//       count,
//       total,
//       page,
//       limit: take,
//       offset: skip,
//       totalPages,
//       status,
//     };
//   }
// }

import { Injectable } from '@nestjs/common';
import { PaginationQueryDto } from './pagination-query.dto';
import { PaginationResult } from '../types';

@Injectable()
export class PaginationService {
  paginate<T>(
    items: T[],
    paginationQuery: PaginationQueryDto,
  ): PaginationResult<T> {
    const page = Math.max(1, paginationQuery.page || 1);
    const limit = Math.min(Math.max(1, paginationQuery.limit || 10), 100); // Ensures 1 ≤ limit ≤ 100
    const skip = (page - 1) * limit;

    const paginatedItems = items.slice(skip, skip + limit);
    const total = items.length;
    const count = paginatedItems.length;
    const totalPages = Math.ceil(total / limit);

    return {
      data: paginatedItems,
      count,
      total,
      page,
      limit,
      offset: skip,
      totalPages,
    };
  }
}
