const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');

// MongoDB connection - Using the correct database with 80+ users
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

// Utility functions for decoding nested data
const gunzip = promisify(zlib.gunzip);
const inflate = promisify(zlib.inflate);

async function decodeNestedData(data) {
  if (!data) return null;
  
  try {
    // If it's already an object, return as is
    if (typeof data === 'object' && !Buffer.isBuffer(data)) {
      return data;
    }
    
    let buffer = data;
    
    // If it's a string, convert to buffer
    if (typeof data === 'string') {
      // Try base64 decode first
      try {
        buffer = Buffer.from(data, 'base64');
      } catch (e) {
        // If base64 fails, use as UTF8
        buffer = Buffer.from(data, 'utf8');
      }
    }
    
    // Try different decompression methods
    try {
      // Try gzip first
      const gzipResult = await gunzip(buffer);
      return JSON.parse(gzipResult.toString());
    } catch (gzipError) {
      try {
        // Try deflate
        const deflateResult = await inflate(buffer);
        return JSON.parse(deflateResult.toString());
      } catch (deflateError) {
        try {
          // Try direct JSON parse
          return JSON.parse(buffer.toString());
        } catch (jsonError) {
          console.warn('All decoding methods failed, returning original data');
          return data;
        }
      }
    }
    
  } catch (error) {
    console.warn('Failed to decode nested data:', error.message);
    return data;
  }
}

// Helper function to convert camelCase to snake_case
function camelToSnakeCase(str) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

// Helper function to safely get nested properties
function safeGet(obj, path, defaultValue = null) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : defaultValue;
  }, obj);
}

async function createFarmerProfile(pgClient, userId, userData) {
  try {
    const farmerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      farm_name: safeGet(userData, 'business.businessName') || safeGet(userData, 'profile.farmName') || null,
      farm_location: safeGet(userData, 'business.businessAddress') || safeGet(userData, 'profile.farmLocation') || null,
      farm_size: safeGet(userData, 'profile.farmSize') || null,
      farm_address: safeGet(userData, 'business.businessAddress') || safeGet(userData, 'profile.farmAddress') || null,
      account_number: safeGet(userData, 'business.accountNumber') || null,
      account_name: safeGet(userData, 'business.accountName') || null,
      bank_name: safeGet(userData, 'business.bankName') || null,
      bank_branch: safeGet(userData, 'business.bankBranch') || null,
      created_at: userData.createdAt || new Date(),
      updated_at: userData.updatedAt || new Date()
    };

    const insertFarmerQuery = `
      INSERT INTO farmers (
        id, user_id, farm_name, farm_location, farm_size, farm_address,
        account_number, account_name, bank_name, bank_branch, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      ON CONFLICT (user_id) DO NOTHING
    `;

    await pgClient.query(insertFarmerQuery, [
      farmerData.id, farmerData.user_id, farmerData.farm_name, farmerData.farm_location,
      farmerData.farm_size, farmerData.farm_address, farmerData.account_number,
      farmerData.account_name, farmerData.bank_name, farmerData.bank_branch,
      farmerData.created_at, farmerData.updated_at
    ]);

    console.log(`Created farmer profile for user: ${userId}`);
  } catch (error) {
    console.error(`Error creating farmer profile for user ${userId}:`, error.message);
  }
}

async function createBuyerProfile(pgClient, userId, userData) {
  try {
    const buyerData = {
      id: crypto.randomUUID(),
      user_id: userId,
      business_name: safeGet(userData, 'business.businessName') || null,
      loyalty_points: 0,
      delivery_preferences: JSON.stringify(safeGet(userData, 'profile.deliveryPreferences') || {}),
      payment_methods: JSON.stringify(safeGet(userData, 'profile.paymentMethods') || {}),
      created_at: userData.createdAt || new Date(),
      updated_at: userData.updatedAt || new Date()
    };

    const insertBuyerQuery = `
      INSERT INTO buyers (
        id, user_id, business_name, loyalty_points, delivery_preferences, payment_methods, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (user_id) DO NOTHING
    `;

    await pgClient.query(insertBuyerQuery, [
      buyerData.id, buyerData.user_id, buyerData.business_name, buyerData.loyalty_points,
      buyerData.delivery_preferences, buyerData.payment_methods, buyerData.created_at, buyerData.updated_at
    ]);

    console.log(`Created buyer profile for user: ${userId}`);
  } catch (error) {
    console.error(`Error creating buyer profile for user ${userId}:`, error.message);
  }
}

async function createAdminProfile(pgClient, userId, userData) {
  try {
    const adminData = {
      id: crypto.randomUUID(),
      user_id: userId,
      role: 'SUPER_ADMIN', // Default role
      permissions: JSON.stringify(['all']),
      department: safeGet(userData, 'profile.department') || null,
      employee_id: safeGet(userData, 'profile.employeeId') || null,
      created_at: userData.createdAt || new Date(),
      updated_at: userData.updatedAt || new Date()
    };

    const insertAdminQuery = `
      INSERT INTO admins (
        id, user_id, role, permissions, department, employee_id, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (user_id) DO NOTHING
    `;

    await pgClient.query(insertAdminQuery, [
      adminData.id, adminData.user_id, adminData.role, adminData.permissions,
      adminData.department, adminData.employee_id, adminData.created_at, adminData.updated_at
    ]);

    console.log(`Created admin profile for user: ${userId}`);
  } catch (error) {
    console.error(`Error creating admin profile for user ${userId}:`, error.message);
  }
}

async function migrateUserAddresses(pgClient, userId, userData) {
  try {
    // Extract addresses from various possible locations
    const addresses = [];
    
    // Check if addresses exist in profile
    if (userData.profile && userData.profile.addresses) {
      if (Array.isArray(userData.profile.addresses)) {
        addresses.push(...userData.profile.addresses);
      } else {
        addresses.push(userData.profile.addresses);
      }
    }
    
    // Check if address exists in business
    if (userData.business && userData.business.businessAddress) {
      addresses.push({
        house_number: '',
        street_name: userData.business.businessAddress,
        community: '',
        lga: userData.business.lga || '',
        state: userData.business.state || '',
        country: 'Nigeria',
        is_default: true
      });
    }
    
    // Check if contact has address
    if (userData.contact && userData.contact.address) {
      addresses.push({
        house_number: '',
        street_name: userData.contact.address,
        community: '',
        lga: userData.contact.lga || '',
        state: userData.contact.state || '',
        country: 'Nigeria',
        is_default: addresses.length === 0
      });
    }

    for (const address of addresses) {
      const addressData = {
        id: crypto.randomUUID(),
        user_id: userId,
        house_number: address.house_number || address.houseNumber || '',
        street_name: address.street_name || address.streetName || address.street || '',
        community: address.community || '',
        lga: address.lga || '',
        state: address.state || '',
        country: address.country || 'Nigeria',
        is_default: address.is_default || address.isDefault || false,
        created_at: new Date(),
        updated_at: new Date()
      };

      const insertAddressQuery = `
        INSERT INTO addresses (
          id, user_id, house_number, street_name, community, lga, state, country, is_default, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `;

      await pgClient.query(insertAddressQuery, [
        addressData.id, addressData.user_id, addressData.house_number, addressData.street_name,
        addressData.community, addressData.lga, addressData.state, addressData.country,
        addressData.is_default, addressData.created_at, addressData.updated_at
      ]);
    }

    if (addresses.length > 0) {
      console.log(`Migrated ${addresses.length} addresses for user: ${userId}`);
    }
  } catch (error) {
    console.error(`Error migrating addresses for user ${userId}:`, error.message);
  }
}

async function migrateUserPreferences(pgClient, userId, userData) {
  try {
    const preferenceData = {
      id: crypto.randomUUID(),
      user_id: userId,
      notification_settings: JSON.stringify(safeGet(userData, 'profile.notificationSettings') || {}),
      privacy_settings: JSON.stringify(safeGet(userData, 'profile.privacySettings') || {}),
      language: safeGet(userData, 'profile.language') || 'en',
      theme: safeGet(userData, 'profile.theme') || 'light',
      currency: safeGet(userData, 'profile.currency') || 'NGN',
      timezone: safeGet(userData, 'profile.timezone') || 'Africa/Lagos',
      created_at: userData.createdAt || new Date(),
      updated_at: userData.updatedAt || new Date()
    };

    const insertPreferenceQuery = `
      INSERT INTO preferences (
        id, user_id, notification_settings, privacy_settings, language, theme, currency, timezone, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      ON CONFLICT (user_id) DO UPDATE SET
        notification_settings = EXCLUDED.notification_settings,
        privacy_settings = EXCLUDED.privacy_settings,
        language = EXCLUDED.language,
        theme = EXCLUDED.theme,
        currency = EXCLUDED.currency,
        timezone = EXCLUDED.timezone,
        updated_at = EXCLUDED.updated_at
    `;

    await pgClient.query(insertPreferenceQuery, [
      preferenceData.id, preferenceData.user_id, preferenceData.notification_settings,
      preferenceData.privacy_settings, preferenceData.language, preferenceData.theme,
      preferenceData.currency, preferenceData.timezone, preferenceData.created_at, preferenceData.updated_at
    ]);

    console.log(`Migrated preferences for user: ${userId}`);
  } catch (error) {
    console.error(`Error migrating preferences for user ${userId}:`, error.message);
  }
}

async function migrateUserNotifications(pgClient, userId, userData) {
  try {
    // Create some default notifications for the user
    const notifications = [
      {
        id: crypto.randomUUID(),
        user_id: userId,
        title: 'Welcome to Eweko!',
        message: 'Your account has been successfully migrated to our new system.',
        type: 'SYSTEM',
        is_read: false,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    for (const notification of notifications) {
      const insertNotificationQuery = `
        INSERT INTO notifications (
          id, user_id, title, message, type, is_read, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `;

      await pgClient.query(insertNotificationQuery, [
        notification.id, notification.user_id, notification.title, notification.message,
        notification.type, notification.is_read, notification.created_at, notification.updated_at
      ]);
    }

    console.log(`Migrated ${notifications.length} notifications for user: ${userId}`);
  } catch (error) {
    console.error(`Error migrating notifications for user ${userId}:`, error.message);
  }
}

async function migrateUsersComprehensive() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    // Connect to both databases
    await mongoClient.connect();
    await pgClient.connect();
    console.log('🔗 Connected to MongoDB and PostgreSQL');

    const db = mongoClient.db(MONGODB_DATABASE);

    // Get all users from fragmented MongoDB collections
    console.log('📊 Fetching users from MongoDB...');

    const buyers = await db.collection('buyers').find({}).toArray();
    const farmers = await db.collection('farmers').find({}).toArray();
    const profiles = await db.collection('profiles').find({}).toArray();
    const contacts = await db.collection('contacts').find({}).toArray();
    const businesses = await db.collection('businesses').find({}).toArray();

    console.log(`Found ${buyers.length} buyers, ${farmers.length} farmers`);
    console.log(`Found ${profiles.length} profiles, ${contacts.length} contacts, ${businesses.length} businesses`);

    // Create a comprehensive user map to handle duplicates
    const userMap = new Map();
    const duplicates = [];

    // Start with contacts as the primary source since they have the emails
    for (const contact of contacts) {
      const decodedContact = await decodeNestedData(contact);
      if (decodedContact && decodedContact.email) {
        const userId = contact._id.toString();
        const email = decodedContact.email;

        // Find corresponding profile and business
        const profile = profiles.find(p => p._id.toString() === userId);
        const business = businesses.find(b => b._id.toString() === userId);
        const buyer = buyers.find(b => b._id.toString() === userId);
        const farmer = farmers.find(f => f._id.toString() === userId);

        const decodedProfile = profile ? await decodeNestedData(profile) : null;
        const decodedBusiness = business ? await decodeNestedData(business) : null;

        // Determine user type based on available data
        let userType = 'BUYER'; // Default
        let sourceData = buyer || {};

        if (farmer) {
          userType = 'FARMER';
          sourceData = farmer;
        } else if (buyer) {
          userType = 'BUYER';
          sourceData = buyer;
        } else {
          // If no buyer/farmer record, determine from profile or business data
          if (decodedBusiness && (decodedBusiness.businessType === 'farm' || decodedBusiness.farmName)) {
            userType = 'FARMER';
          }
        }

        if (userMap.has(email)) {
          duplicates.push({ email, existing: userMap.get(email), duplicate: { contact: decodedContact } });
        } else {
          userMap.set(email, {
            _id: contact._id,
            email: email,
            user_type: userType,
            source: userType.toLowerCase() + 's',
            profile: decodedProfile,
            contact: decodedContact,
            business: decodedBusiness,
            // Include any additional data from buyer/farmer records
            ...sourceData,
            // Override with contact email
            email: email
          });
        }
      }
    }

    console.log(`Found ${duplicates.length} duplicate users`);
    console.log(`Total unique users to migrate: ${userMap.size}`);

    let migratedCount = 0;
    let errorCount = 0;

    // Migrate each unique user with all their related data
    for (const [email, user] of userMap) {
      try {
        console.log(`\n--- Migrating user: ${email} (${user.user_type}) ---`);

        // Use the already decoded data from the user object
        const profile = user.profile;
        const contact = user.contact;
        const business = user.business;

        // Create comprehensive user data
        const userId = crypto.randomUUID();
        const userData = {
          id: userId,
          username: email,
          password: user.password || '', // Will be empty for most users
          user_type: user.user_type,
          verified: user.verified || false,
          is_active: user.isActive !== false, // Default to true unless explicitly false
          last_login: user.lastLogin || null,

          // Profile data
          first_name: (profile && profile.firstName) || user.firstName || 'Unknown',
          last_name: (profile && profile.lastName) || user.lastName || 'User',
          middle_name: (profile && profile.middleName) || user.middleName || null,
          prefix: (profile && profile.prefix) || user.prefix || null,
          gender: (profile && profile.gender) || user.gender || null,
          date_of_birth: (profile && profile.dateOfBirth) || user.dateOfBirth || null,
          profile_picture: (profile && profile.profilePicture) || user.profilePicture || null,

          // Contact data
          email: email,
          primary_phone: (contact && contact.primaryPhone) || user.phoneNumber || user.phone || null,
          secondary_phone: (contact && contact.secondaryPhone) || null,

          // Verification status
          is_premium: user.isPremium || false,
          is_phone_verified: user.isPhoneVerified || false,
          is_email_verified: user.isEmailVerified || false,

          // Timestamps
          created_at: user.createdAt || new Date(),
          updated_at: user.updatedAt || new Date(),

          // Store decoded data for profile creation
          profile: profile,
          contact: contact,
          business: business
        };

        // Insert user into PostgreSQL
        const insertUserQuery = `
          INSERT INTO users (
            id, username, password, user_type, verified, is_active, last_login,
            first_name, last_name, middle_name, prefix, gender, date_of_birth, profile_picture,
            email, primary_phone, secondary_phone, is_premium, is_phone_verified, is_email_verified,
            created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
          ) ON CONFLICT (email) DO NOTHING
          RETURNING id
        `;

        const userResult = await pgClient.query(insertUserQuery, [
          userData.id, userData.username, userData.password, userData.user_type,
          userData.verified, userData.is_active, userData.last_login,
          userData.first_name, userData.last_name, userData.middle_name, userData.prefix,
          userData.gender, userData.date_of_birth, userData.profile_picture,
          userData.email, userData.primary_phone, userData.secondary_phone,
          userData.is_premium, userData.is_phone_verified, userData.is_email_verified,
          userData.created_at, userData.updated_at
        ]);

        if (userResult.rows.length === 0) {
          console.log(`User ${userData.email} already exists, skipping...`);
          continue;
        }

        const insertedUserId = userResult.rows[0].id;
        console.log(`✅ Inserted user: ${userData.email} with ID: ${insertedUserId}`);

        // Create type-specific profile
        if (userData.user_type === 'FARMER') {
          await createFarmerProfile(pgClient, insertedUserId, userData);
        } else if (userData.user_type === 'BUYER') {
          await createBuyerProfile(pgClient, insertedUserId, userData);
        } else if (userData.user_type === 'ADMIN') {
          await createAdminProfile(pgClient, insertedUserId, userData);
        }

        // Migrate all related data atomically
        await migrateUserAddresses(pgClient, insertedUserId, userData);
        await migrateUserPreferences(pgClient, insertedUserId, userData);
        await migrateUserNotifications(pgClient, insertedUserId, userData);

        migratedCount++;
        console.log(`✅ Successfully migrated user ${migratedCount}: ${userData.email} with all related data`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating user ${email}:`, error.message);
      }
    }

    console.log(`\n📊 MIGRATION SUMMARY:`);
    console.log(`✅ Successfully migrated: ${migratedCount} users`);
    console.log(`❌ Errors: ${errorCount} users`);
    console.log(`📋 Duplicates found: ${duplicates.length}`);

  } catch (error) {
    console.error('❌ Comprehensive migration failed:', error);
    throw error;
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateCategories() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();
    console.log('🔗 Connected for categories migration');

    const db = mongoClient.db(MONGODB_DATABASE);
    const categories = await db.collection('categories').find({}).toArray();

    console.log(`📊 Found ${categories.length} categories to migrate`);

    let migratedCount = 0;

    for (const category of categories) {
      try {
        const categoryData = {
          id: crypto.randomUUID(),
          name: category.name,
          slug: category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
          description: category.description || null,
          image: category.imageUrl || category.image_url || category.image || null,
          is_active: category.isActive !== false,
          created_at: category.createdAt || new Date(),
          updated_at: category.updatedAt || new Date()
        };

        const insertCategoryQuery = `
          INSERT INTO categories (id, name, slug, description, image, is_active, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          ON CONFLICT (name) DO NOTHING
        `;

        await pgClient.query(insertCategoryQuery, [
          categoryData.id, categoryData.name, categoryData.slug, categoryData.description,
          categoryData.image, categoryData.is_active, categoryData.created_at, categoryData.updated_at
        ]);

        migratedCount++;
        console.log(`Migrated category: ${categoryData.name}`);

      } catch (error) {
        console.error(`Error migrating category ${category.name}:`, error.message);
      }
    }

    console.log(`✅ Successfully migrated ${migratedCount} categories`);

  } catch (error) {
    console.error('❌ Categories migration failed:', error);
    throw error;
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function migrateProduces() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();
    console.log('🔗 Connected for produces migration');

    const db = mongoClient.db(MONGODB_DATABASE);
    const produces = await db.collection('produces').find({}).toArray();

    console.log(`📊 Found ${produces.length} produces to migrate`);

    // Get category and farmer mappings
    const categories = await pgClient.query('SELECT id, name FROM categories');
    const farmers = await pgClient.query('SELECT id, user_id FROM farmers');
    const users = await pgClient.query('SELECT id, email FROM users WHERE user_type = \'FARMER\'');

    const categoryMap = new Map();
    categories.rows.forEach(cat => categoryMap.set(cat.name, cat.id));

    const farmerMap = new Map();
    farmers.rows.forEach(farmer => farmerMap.set(farmer.user_id, farmer.id));

    const userEmailMap = new Map();
    users.rows.forEach(user => userEmailMap.set(user.email, user.id));

    let migratedCount = 0;

    for (const produce of produces) {
      try {
        // Find the farmer for this produce
        let farmerId = null;
        if (produce.farmer_id || produce.farmerId) {
          farmerId = farmerMap.get(produce.farmer_id || produce.farmerId);
        }

        // Find category
        let categoryId = null;
        if (produce.category) {
          categoryId = categoryMap.get(produce.category);
        }

        const produceData = {
          id: crypto.randomUUID(),
          name: produce.name,
          slug: produce.slug || produce.name.toLowerCase().replace(/\s+/g, '-'),
          description: produce.description || null,
          price: parseFloat(produce.price) || 0,
          negotiable_price: parseFloat(produce.negotiablePrice || produce.negotiable_price || produce.price) || 0,
          min_order_qty: parseInt(produce.minOrderQty || produce.min_order_qty || 1),
          stock: parseInt(produce.stock) || 0,
          category_id: categoryId,
          farmer_id: farmerId,
          images: JSON.stringify(produce.imageUrls || produce.image_urls || produce.images || []),
          harvest_date: produce.harvestDate || produce.harvest_date || null,
          created_at: produce.createdAt || new Date(),
          updated_at: produce.updatedAt || new Date()
        };

        const insertProduceQuery = `
          INSERT INTO produces (
            id, name, slug, description, price, negotiable_price, min_order_qty, stock,
            category_id, farmer_id, images, harvest_date, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        `;

        await pgClient.query(insertProduceQuery, [
          produceData.id, produceData.name, produceData.slug, produceData.description,
          produceData.price, produceData.negotiable_price, produceData.min_order_qty,
          produceData.stock, produceData.category_id, produceData.farmer_id,
          produceData.images, produceData.harvest_date, produceData.created_at, produceData.updated_at
        ]);

        migratedCount++;
        console.log(`Migrated produce: ${produceData.name}`);

      } catch (error) {
        console.error(`Error migrating produce ${produce.name}:`, error.message);
      }
    }

    console.log(`✅ Successfully migrated ${migratedCount} produces`);

  } catch (error) {
    console.error('❌ Produces migration failed:', error);
    throw error;
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

async function runConsolidatedMigration() {
  console.log('🚀 Starting Consolidated MongoDB to PostgreSQL Migration...\n');

  try {
    // Step 1: Migrate categories first (needed for produces)
    console.log('=== STEP 1: Migrating Categories ===');
    await migrateCategories();

    // Step 2: Migrate users comprehensively with all related data
    console.log('\n=== STEP 2: Migrating Users (Comprehensive with all related data) ===');
    await migrateUsersComprehensive();

    // Step 3: Migrate produces
    console.log('\n=== STEP 3: Migrating Produces ===');
    await migrateProduces();

    console.log('\n🎉 CONSOLIDATED MIGRATION COMPLETED SUCCESSFULLY! 🎉');
    console.log('✅ All users have been migrated with their complete data:');
    console.log('   - User profiles and authentication data');
    console.log('   - Type-specific profiles (farmer/buyer/admin)');
    console.log('   - Addresses');
    console.log('   - Preferences');
    console.log('   - Notifications');
    console.log('   - Categories and produces');
    console.log('\n📊 Each user migration was atomic - if a user was migrated, all their data was migrated together.');

  } catch (error) {
    console.error('❌ Consolidated migration failed:', error);
    process.exit(1);
  }
}

// Run the consolidated migration
if (require.main === module) {
  runConsolidatedMigration().catch(console.error);
}

module.exports = {
  runConsolidatedMigration,
  migrateUsersComprehensive,
  migrateCategories,
  migrateProduces
};
