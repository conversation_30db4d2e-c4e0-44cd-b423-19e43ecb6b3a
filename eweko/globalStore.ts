import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { MaskedAccountDetails, SignupFormData } from './app/(auth)/auth/types';
import { Address, Cart } from './app/(buyers)/buyers/types';
import { IdCard } from 'lucide-react';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  userType: string;
  profilePicture: string | null;
}

interface GlobalState {
  mobileNav: boolean;
  toggleMobileNav: (mobileNav: boolean) => void;
  isSidebarOpen: boolean;
  toggleSidebarOpen: (isSidebarOpen: boolean) => void;
  isContentReady: boolean;
  setIsContentReady: (isContentReady: boolean) => void;
  isAddToCartModalOpen: boolean;
  setIsAddToCartModalOpen: (isAddToCartModalOpen: boolean) => void;
  signupData: SignupFormData | undefined;
  setSignupData: (signupData: SignupFormData | undefined) => void;
  produceToAdd: any;
  setProduceToAdd: (produceToAdd: any) => void;

  maskedDetails: MaskedAccountDetails;
  setMaskedDetails: (maskedDetails: MaskedAccountDetails) => void;

  accountCreated: boolean;
  setAccountCreated: (accountCreated: boolean) => void;

  selectedOTPchannel: string | undefined;
  setSelectedOTPchannel: (selectedOTPchannel: string | undefined) => void;

  isCheckoutReady: boolean;
  setIsCheckoutReady: (isCheckoutReady: boolean) => void;

  addressStep: number;
  setAddressStep: (addressStep: number) => void;

  settingsAddressStep: number;
  setSettingsAddressStep: (addressStep: number) => void;

  address2edit: string | null;
  setAddress2edit: (address2edit: string | null) => void;

  selectedAddress: Address | null;
  setSelectedAddress: (selectedAddress: Address | null) => void;

  selectedDelivery: string | null;
  setSelectedDelivery: (selectedDelivery: string | null) => void;

  selectedPayment: string | null;
  setSelectedPayment: (selectedPayment: string | null) => void;

  isAddressOpen: boolean;
  setIsAddressOpen: (isAddressOpen: boolean) => void;

  settingsAddrAdded: boolean;
  setSettingsAddrAdded: (settingsAddrAdded: boolean) => void;

  settingsAddrEdited: boolean;
  setSettingsAddrEdited: (settingsAddrEdited: boolean) => void;

  isDeliveryOpen: boolean;
  setIsDeliveryOpen: (isDeliveryOpen: boolean) => void;
  isPaymentOpen: boolean;
  setIsPaymentOpen: (isPaymentOpen: boolean) => void;

  authToken: string | null;
  setAuthToken: (authToken: string | null) => void;
  userLoginId: string | undefined;
  setUserLoginId: (userLoginId: string | undefined) => void;

  passwordResetPhoneNumber: string | null;
  setPasswordResetPhoneNumber: (
    passwordResetPhoneNumber: string | null
  ) => void;
  passwordResetOtp: string | undefined;
  setPasswordResetOtp: (passwordResetOtp: string | undefined) => void;

  loggedInUser: User | null;
  setLoggedInUser: (user: User | null) => void;

  cart: Cart | null;
  setCart: (cart: Cart | null) => void;
  clearCart: () => void;

  addresses: Address[] | null;
  setAddresses: (addresses: Address[] | null) => void;

  produceUpdated: boolean;
  setProduceUpdated: (produceUpdated: boolean) => void;

  cplId: string;
  setCplId: (cplId: string) => void;
  cpoId: string;
  setCpoId: (cpoId: string) => void;
  cptId: string;
  setCptId: (cptId: string) => void;
}

export const useGlobalState = create<GlobalState>()(
  persist(
    set => ({
      mobileNav: false,
      toggleMobileNav: open => set({ mobileNav: open }),
      isSidebarOpen: true,
      toggleSidebarOpen: open => set({ isSidebarOpen: open }),
      isContentReady: false,
      setIsContentReady: ready => set({ isContentReady: ready }),
      isAddToCartModalOpen: false,
      setIsAddToCartModalOpen: open => set({ isAddToCartModalOpen: open }),
      signupData: undefined,
      setSignupData: data => set({ signupData: data }),
      produceToAdd: undefined,
      setProduceToAdd: data => set({ produceToAdd: data }),

      maskedDetails: { email: '', primaryPhone: '' },
      setMaskedDetails: data => set({ maskedDetails: data }),

      accountCreated: false,
      setAccountCreated: status => set({ accountCreated: status }),

      selectedOTPchannel: undefined,
      setSelectedOTPchannel: channel => set({ selectedOTPchannel: channel }),

      isCheckoutReady: false,
      setIsCheckoutReady: ready => set({ isCheckoutReady: ready }),

      addressStep: 1,
      setAddressStep: step => set({ addressStep: step }),

      settingsAddressStep: 1,
      setSettingsAddressStep: step => set({ settingsAddressStep: step }),

      settingsAddrAdded: false,
      setSettingsAddrAdded: open => set({ settingsAddrAdded: open }),

      settingsAddrEdited: false,
      setSettingsAddrEdited: open => set({ settingsAddrEdited: open }),

      address2edit: '',
      setAddress2edit: id => set({ address2edit: id }),

      selectedAddress: null,
      setSelectedAddress: address => set({ selectedAddress: address }),

      selectedDelivery: null,
      setSelectedDelivery: delivery => set({ selectedDelivery: delivery }),

      selectedPayment: null,
      setSelectedPayment: payment => set({ selectedPayment: payment }),

      isAddressOpen: true,
      setIsAddressOpen: open => set({ isAddressOpen: open }),
      isDeliveryOpen: false,
      setIsDeliveryOpen: open => set({ isDeliveryOpen: open }),
      isPaymentOpen: false,
      setIsPaymentOpen: open => set({ isPaymentOpen: open }),

      authToken: null,
      setAuthToken: token => set({ authToken: token }),

      userLoginId: undefined,
      setUserLoginId: userId => set({ userLoginId: userId }),

      passwordResetPhoneNumber: null,
      setPasswordResetPhoneNumber: userId =>
        set({ passwordResetPhoneNumber: userId }),
      passwordResetOtp: undefined,
      setPasswordResetOtp: userId => set({ passwordResetOtp: userId }),

      loggedInUser: null,
      setLoggedInUser: user => set({ loggedInUser: user }),

      cart: null,
      setCart: cart => set({ cart }),
      clearCart: () => set({ cart: null }),

      addresses: null,
      setAddresses: addresses => set({ addresses }),

      produceUpdated: false,
      setProduceUpdated: open => set({ produceUpdated: open }),

      cplId: '',
      setCplId: id => set({ cplId: id }),
      cpoId: '',
      setCpoId: oId => set({ cpoId: oId }),
      cptId: '',
      setCptId: tId => set({ cptId: tId }),
    }),
    {
      name: 'eweko-store',
      storage: createJSONStorage(() => sessionStorage),
      partialize: state => ({
        authToken: state.authToken,
        loggedInUser: state.loggedInUser,
        passwordResetPhoneNumber: state.passwordResetPhoneNumber,
        cart: state.cart,
        produceUpdated: state.produceUpdated,
        selectedDelivery: state.selectedDelivery,
        selectedPayment: state.selectedPayment,
        cplId: state.cplId,
        cpoId: state.cpoId,
        cptId: state.cptId,
      }),
    }
  )
);
