import { clsx, type ClassValue } from 'clsx';

import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function maskText(text: string): string {
  if (text && text.length <= 4) {
    return '*'.repeat(text.length);
  }

  if (text) {
    const firstLength = Math.floor(text.length / 3);
    const lastLength = Math.floor(text.length / 3);
    const middleLength = text.length - firstLength - lastLength;

    const firstPart = text.slice(0, firstLength);
    const middlePart = text
      .slice(firstLength, firstLength + middleLength)
      .replace(/./g, '*');
    const lastPart = text.slice(firstLength + middleLength);

    return `${firstPart}${middlePart}${lastPart}`;
  } else {
    return '';
  }
}

export function capitalizeFirstLetter(name: string | undefined): string {
  name = name ? name : 'ewe<PERSON>';
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
}

import { jwtVerify } from 'jose';
import { IJwtPayload } from '../app/(buyers)/buyers/types';

const JWT_SECRET = process.env.NEXT_PUBLIC_JWT_SECRET;

export async function verifyToken(token: string) {
  try {
    const encoder = new TextEncoder();
    const { payload } = await jwtVerify(token, encoder.encode(JWT_SECRET));

    const typedPayload: IJwtPayload = {
      id: payload.id as string,
      firstName: payload.firstName as string,
      lastName: payload.lastName as string,
      email: payload.email as string,
      phone: payload.phone as string,
      userType: payload.userType as
        | 'BUYER'
        | 'FARMER'
        | 'ADMIN'
        | 'AGENT'
        | 'LOGISTICS',
      profilePicture: payload.profilePicture as string,
      iat: payload.iat as number,
    };

    return {
      isValid: true,
      payload: typedPayload,
    };
  } catch (error: any) {
    console.error('Token Verification Detailed Error:', {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
    });

    return {
      isValid: false,
      error:
        error instanceof Error ? error.message : 'Token verification failed',
    };
  }
}
